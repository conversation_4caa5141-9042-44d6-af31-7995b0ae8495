<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار العرض</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-align: center;
            padding: 50px;
            margin: 0;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            max-width: 600px;
            margin: 0 auto;
        }
        h1 {
            font-size: 2rem;
            margin-bottom: 20px;
        }
        .button {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 10px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .button:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار العرض - المنصة الأكاديمية</h1>
        <p>د. محمد يعقوب إسماعيل يعقوب</p>
        <p>نائب عميد كلية الهندسة - جامعة السودان للعلوم والتكنولوجيا</p>
        
        <div style="margin-top: 30px;">
            <a href="pages/homepage_nuclear_medicine_academic_hub.html" class="button">
                📚 المكتبة الأكاديمية
            </a>
            <a href="book_authoring_hub.html" class="button">
                ✍️ مشروع التأليف
            </a>
        </div>
        
        <div style="margin-top: 30px;">
            <a href="index.html" class="button">الصفحة الرئيسية الكاملة</a>
            <a href="index_simple.html" class="button">النسخة المبسطة</a>
            <a href="index_tailwind.html" class="button">نسخة Tailwind</a>
        </div>
    </div>
    
    <script>
        console.log('تم تحميل الصفحة بنجاح!');
        alert('مرحباً! إذا رأيت هذه الرسالة فالصفحة تعمل بشكل صحيح.');
    </script>
</body>
</html>
