<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر كتاب مبادئ الحماية من الإشعاع في التطبيقات الطبية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .author-info {
            color: #34495e;
            font-size: 1.1em;
            margin-bottom: 15px;
        }

        .main-layout {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 30px;
            height: calc(100vh - 200px);
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .sidebar h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3em;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .chapter-list {
            list-style: none;
        }

        .chapter-item {
            margin-bottom: 15px;
            padding: 12px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .chapter-item:hover {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .chapter-item.active {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            border-color: #c0392b;
        }

        .chapter-item h4 {
            font-size: 1.1em;
            margin-bottom: 5px;
        }

        .chapter-item p {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .editor-area {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .toolbar {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        .toolbar button {
            padding: 10px 15px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: 1px solid transparent;
        }

        .toolbar button:hover {
            background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(52, 152, 219, 0.3);
        }

        .current-chapter {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #e74c3c;
        }

        .current-chapter h2 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.5em;
        }

        .current-chapter p {
            color: #7f8c8d;
            margin-bottom: 15px;
        }

        .content-editor {
            width: 100%;
            min-height: 400px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1.1em;
            line-height: 1.8;
            font-family: 'Arial', sans-serif;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .content-editor:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.2);
        }

        .stats {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            font-size: 0.9em;
            color: #7f8c8d;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .action-buttons button {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .save-btn {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
        }

        .save-btn:hover {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(46, 204, 113, 0.3);
        }

        .export-btn {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .export-btn:hover {
            background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
        }

        .preview-btn {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            color: white;
        }

        .preview-btn:hover {
            background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(155, 89, 182, 0.3);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            max-width: 80%;
            max-height: 80%;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .close-modal {
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #aaa;
            transition: color 0.3s ease;
        }

        .close-modal:hover {
            color: #e74c3c;
        }

        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 15px 25px;
            border-radius: 10px;
            color: white;
            font-weight: bold;
            z-index: 1100;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .notification.show {
            opacity: 1;
            transform: translateX(-50%) translateY(20px);
        }

        .notification.success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        }

        .notification.error {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        @media (max-width: 768px) {
            .main-layout {
                grid-template-columns: 1fr;
                height: auto;
            }
            
            .sidebar {
                order: 2;
                max-height: 300px;
            }
            
            .header h1 {
                font-size: 1.8em;
            }
            
            .toolbar {
                justify-content: center;
            }
        }

        .chapter-content {
            display: none;
        }

        .chapter-content.active {
            display: block;
        }

        .outline-section {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .outline-section h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .outline-section ul {
            margin-right: 20px;
        }

        .outline-section li {
            margin-bottom: 5px;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>محرر كتاب مبادئ الحماية من الإشعاع في التطبيقات الطبية</h1>
            <div class="author-info">
                <strong>المؤلف:</strong> د. محمد يعقوب إسماعيل يعقوب<br>
                <strong>المنصب:</strong> نائب عميد كلية الهندسة - أستاذ مساعد الهندسة الطبية الحيوية<br>
                <strong>الجامعة:</strong> جامعة السودان للعلوم والتكنولوجيا<br>
                <strong>حقوق النشر:</strong> © 2025 | البريد الإلكتروني: <EMAIL>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="main-layout">
            <div class="sidebar">
                <h3>فهرس الكتاب</h3>
                <ul class="chapter-list" id="chapterList">
                    <!-- سيتم ملء الفصول بواسطة JavaScript -->
                </ul>
            </div>

            <div class="editor-area">
                <div class="toolbar">
                    <button onclick="formatText('bold')">عريض</button>
                    <button onclick="formatText('italic')">مائل</button>
                    <button onclick="formatText('underline')">تحته خط</button>
                    <button onclick="insertHeading()">عنوان رئيسي</button>
                    <button onclick="insertSubheading()">عنوان فرعي</button>
                    <button onclick="insertList()">قائمة</button>
                    <button onclick="insertEquation()">معادلة</button>
                    <button onclick="insertTable()">جدول</button>
                    <button onclick="insertFigure()">شكل</button>
                </div>

                <div class="current-chapter" id="currentChapter">
                    <h2>مرحباً بك في محرر الكتاب</h2>
                    <p>اختر فصلاً من القائمة الجانبية للبدء في الكتابة</p>
                </div>

                <textarea class="content-editor" id="contentEditor" placeholder="ابدأ الكتابة هنا..."></textarea>

                <div class="stats">
                    <span>عدد الكلمات: <span id="wordCount">0</span></span>
                    <span>عدد الأحرف: <span id="charCount">0</span></span>
                    <span>وقت الكتابة: <span id="writingTime">0 دقيقة</span></span>
                </div>

                <div class="action-buttons">
                    <button class="save-btn" onclick="saveContent()">حفظ المحتوى</button>
                    <button class="export-btn" onclick="exportBook()">تصدير الكتاب</button>
                    <button class="preview-btn" onclick="previewChapter()">معاينة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal للمعاينة -->
    <div class="modal" id="previewModal">
        <div class="modal-content">
            <span class="close-modal" onclick="closeModal()">&times;</span>
            <div id="previewContent"></div>
        </div>
    </div>

    <!-- إشعارات -->
    <div class="notification" id="notification"></div>

    <script>
        // بيانات الكتاب وفصوله
        const bookData = {
            title: "مبادئ الحماية من الإشعاع في التطبيقات الطبية",
            author: "د. محمد يعقوب إسماعيل يعقوب",
            chapters: [
                {
                    id: 1,
                    title: "التأثيرات البيولوجية للإشعاع",
                    part: "الجزء الأول: الأسس البيولوجية والتنظيمية",
                    outline: [
                        "آليات تفاعل الإشعاع على المستوى الخلوي والجزيئي",
                        "التأثيرات الحتمية والعشوائية",
                        "العوامل المؤثرة على الاستجابة البيولوجية",
                        "الحساسية الإشعاعية للأنسجة والأعضاء"
                    ],
                    content: ""
                },
                {
                    id: 2,
                    title: "الكميات والوحدات الإشعاعية",
                    part: "الجزء الأول: الأسس البيولوجية والتنظيمية",
                    outline: [
                        "الكميات الفيزيائية (التعرض، الكيرما)",
                        "كميات الحماية الإشعاعية",
                        "عوامل الترجيح الإشعاعي والأنسجة",
                        "العلاقة بين الكميات وحسابها"
                    ],
                    content: ""
                },
                {
                    id: 3,
                    title: "فلسفة ونظام الحماية من الإشعاع",
                    part: "الجزء الأول: الأسس البيولوجية والتنظيمية",
                    outline: [
                        "الهيئات الدولية ودورها",
                        "مبدأ التبرير",
                        "مبدأ الأمثلية (ALARA)",
                        "تحديد الجرعات والحدود"
                    ],
                    content: ""
                },
                {
                    id: 4,
                    title: "حماية المريض والعاملين في التصوير الإشعاعي",
                    part: "الجزء الثاني: الحماية في الأشعة التشخيصية",
                    outline: [
                        "تصميم الغرف والتدريع",
                        "معدات الوقاية الشخصية",
                        "تقنيات تقليل الجرعة",
                        "المستويات المرجعية التشخيصية"
                    ],
                    content: ""
                },
                {
                    id: 5,
                    title: "التعامل الآمن مع المصادر المشعة",
                    part: "الجزء الثالث: الحماية في الطب النووي",
                    outline: [
                        "تصميم المختبرات الساخنة",
                        "مبادئ ALARA في الطب النووي",
                        "تقنيات التعامل مع المواد المشعة",
                        "تدريع الحقن والحاويات"
                    ],
                    content: ""
                },
                {
                    id: 6,
                    title: "مراقبة التلوث وإدارة النفايات المشعة",
                    part: "الجزء الثالث: الحماية في الطب النووي",
                    outline: [
                        "أنواع التلوث الإشعاعي",
                        "أجهزة مسح التلوث",
                        "إجراءات إزالة التلوث",
                        "تصنيف ومعالجة النفايات"
                    ],
                    content: ""
                },
                {
                    id: 7,
                    title: "الحماية في العلاج الإشعاعي الخارجي",
                    part: "الجزء الرابع: الحماية في العلاج الإشعاعي",
                    outline: [
                        "تصميم غرف المسرعات الخطية",
                        "حسابات التدريع",
                        "ميزات السلامة وأنظمة الإغلاق",
                        "الحماية من الإشعاع المتسرب"
                    ],
                    content: ""
                },
                {
                    id: 8,
                    title: "الحماية في العلاج الإشعاعي الموضعي",
                    part: "الجزء الرابع: الحماية في العلاج الإشعاعي",
                    outline: [
                        "التعامل مع المصادر المختومة",
                        "تدريع غرف العلاج",
                        "إجراءات نقل وزرع المصادر",
                        "مراقبة المريض والمنطقة"
                    ],
                    content: ""
                },
                {
                    id: 9,
                    title: "المراقبة الإشعاعية الشخصية والبيئية",
                    part: "الجزء الخامس: المراقبة والثقافة التنظيمية",
                    outline: [
                        "أهداف وأنواع المراقبة",
                        "أجهزة قياس الجرعات الشخصية",
                        "برامج المراقبة البيئية",
                        "تسجيل وتحليل سجلات الجرعات"
                    ],
                    content: ""
                },
                {
                    id: 10,
                    title: "بناء ثقافة السلامة الإشعاعية",
                    part: "الجزء الخامس: المراقبة والثقافة التنظيمية",
                    outline: [
                        "مسؤوليات الإدارة ومسؤول الحماية",
                        "أهمية التدريب المستمر",
                        "التحقيق في الحوادث",
                        "التشريعات الوطنية والرقابة"
                    ],
                    content: ""
                }
            ]
        };

        // المتغيرات العامة
        let currentChapterIndex = 0;
        let writingStartTime = Date.now();
        let savedContent = {};

        // تحميل المحتوى المحفوظ
        function loadSavedContent() {
            const saved = localStorage.getItem('radiationBookContent');
            if (saved) {
                savedContent = JSON.parse(saved);
                bookData.chapters.forEach(chapter => {
                    if (savedContent[chapter.id]) {
                        chapter.content = savedContent[chapter.id];
                    }
                });
            }
        }

        // إنشاء قائمة الفصول
        function createChapterList() {
            const chapterList = document.getElementById('chapterList');
            chapterList.innerHTML = '';
            
            bookData.chapters.forEach((chapter, index) => {
                const li = document.createElement('li');
                li.className = 'chapter-item';
                li.onclick = () => selectChapter(index);
                
                li.innerHTML = `
                    <h4>الفصل ${chapter.id}: ${chapter.title}</h4>
                    <p>${chapter.part}</p>
                `;
                
                chapterList.appendChild(li);
            });
        }

        // اختيار فصل
        function selectChapter(index) {
            // حفظ المحتوى الحالي
            if (currentChapterIndex !== null) {
                saveCurrentChapter();
            }
            
            currentChapterIndex = index;
            const chapter = bookData.chapters[index];
            
            // تحديث الواجهة
            document.querySelectorAll('.chapter-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelectorAll('.chapter-item')[index].classList.add('active');
            
            // تحديث المحتوى
            document.getElementById('currentChapter').innerHTML = `
                <h2>الفصل ${chapter.id}: ${chapter.title}</h2>
                <p><strong>القسم:</strong> ${chapter.part}</p>
                <div class="outline-section">
                    <h4>موضوعات الفصل:</h4>
                    <ul>
                        ${chapter.outline.map(item => `<li>${item}</li>`).join('')}
                    </ul>
                </div>
            `;
            
            document.getElementById('contentEditor').value = chapter.content || '';
            updateStats();
            updateProgress();
        }

        // حفظ الفصل الحالي
        function saveCurrentChapter() {
            if (currentChapterIndex !== null) {
                const content = document.getElementById('contentEditor').value;
                bookData.chapters[currentChapterIndex].content = content;
                savedContent[bookData.chapters[currentChapterIndex].id] = content;
                localStorage.setItem('radiationBookContent', JSON.stringify(savedContent));
            }
        }

        // تحديث الإحصائيات
        function updateStats() {
            const content = document.getElementById('contentEditor').value;
            const wordCount = content.trim() ? content.trim().split(/\s+/).length : 0;
            const charCount = content.length;
            const writingTime = Math.floor((Date.now() - writingStartTime) / 60000);
            
            document.getElementById('wordCount').textContent = wordCount;
            document.getElementById('charCount').textContent = charCount;
            document.getElementById('writingTime').textContent = writingTime;
        }

        // تحديث شريط التقدم
        function updateProgress() {
            const totalChapters = bookData.chapters.length;
            const completedChapters = bookData.chapters.filter(ch => ch.content && ch.content.trim().length > 100).length;
            const progress = (completedChapters / totalChapters) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // وظائف التنسيق
        function formatText(command) {
            const editor = document.getElementById('contentEditor');
            const start = editor.selectionStart;
            const end = editor.selectionEnd;
            const selectedText = editor.value.substring(start, end);
            
            if (selectedText) {
                let formattedText = selectedText;
                switch (command) {
                    case 'bold':
                        formattedText = `**${selectedText}**`;
                        break;
                    case 'italic':
                        formattedText = `*${selectedText}*`;
                        break;
                    case 'underline':
                        formattedText = `<u>${selectedText}</u>`;
                        break;
                }
                
                editor.value = editor.value.substring(0, start) + formattedText + editor.value.substring(end);
                editor.focus();
                editor.setSelectionRange(start, start + formattedText.length);
            }
        }

        function insertHeading() {
            insertAtCursor('\n\n# عنوان رئيسي\n\n');
        }

        function insertSubheading() {
            insertAtCursor('\n\n## عنوان فرعي\n\n');
        }

        function insertList() {
            insertAtCursor('\n\n- العنصر الأول\n- العنصر الثاني\n- العنصر الثالث\n\n');
        }

        function insertEquation() {
            insertAtCursor('\n\nالمعادلة: D = D₀ × e^(-μx)\n\nحيث:\n- D: الجرعة بعد التدريع\n- D₀: الجرعة الأولية\n- μ: معامل التوهين\n- x: سمك المادة\n\n');
        }

        function insertTable() {
            insertAtCursor('\n\n| العنصر | القيمة | الوحدة |\n|--------|-------|-------|\n| المثال 1 | 100 | mSv |\n| المثال 2 | 50 | mSv |\n\n');
        }

        function insertFigure() {
            insertAtCursor('\n\n[شكل رقم X: وصف الشكل]\n\n');
        }

        function insertAtCursor(text) {
            const editor = document.getElementById('contentEditor');
            const start = editor.selectionStart;
            const end = editor.selectionEnd;
            editor.value = editor.value.substring(0, start) + text + editor.value.substring(end);
            editor.focus();
            editor.setSelectionRange(start + text.length, start + text.length);
        }

        // حفظ المحتوى
        function saveContent() {
            saveCurrentChapter();
            showNotification('تم حفظ المحتوى بنجاح!', 'success');
        }

        // تصدير الكتاب
        function exportBook() {
            saveCurrentChapter();
            
            let fullBook = `# ${bookData.title}\n\n`;
            fullBook += `**المؤلف:** ${bookData.author}\n\n`;
            fullBook += `---\n\n`;
            
            bookData.chapters.forEach(chapter => {
                fullBook += `\n# الفصل ${chapter.id}: ${chapter.title}\n\n`;
                fullBook += `**القسم:** ${chapter.part}\n\n`;
                fullBook += `## موضوعات الفصل:\n\n`;
                chapter.outline.forEach(item => {
                    fullBook += `- ${item}\n`;
                });
                fullBook += `\n## المحتوى:\n\n`;
                fullBook += chapter.