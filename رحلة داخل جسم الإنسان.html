<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رحلة داخل جسم الإنسان: دليل مبسط للتصوير الطبي النووي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .book-header {
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .book-title {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .author-info {
            font-size: 1.1rem;
            color: #7f8c8d;
            margin-bottom: 20px;
        }

        .book-description {
            font-size: 1.2rem;
            color: #34495e;
            line-height: 1.8;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(52, 152, 219, 0.1);
            border-radius: 15px;
        }

        .navigation {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .nav-btn.active {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }

        .content-section {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .content-section.active {
            display: block;
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .section-title {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 20px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        .chapter {
            margin-bottom: 25px;
            padding: 20px;
            background: rgba(52, 152, 219, 0.05);
            border-radius: 15px;
            border-right: 5px solid #3498db;
        }

        .chapter-title {
            font-size: 1.5rem;
            color: #2980b9;
            margin-bottom: 15px;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .chapter-title:hover {
            color: #e74c3c;
        }

        .chapter-content {
            display: none;
            font-size: 1.1rem;
            line-height: 1.8;
            color: #34495e;
        }

        .chapter-content.active {
            display: block;
            animation: slideDown 0.3s ease-in-out;
        }

        @keyframes slideDown {
            from { opacity: 0; max-height: 0; }
            to { opacity: 1; max-height: 500px; }
        }

        .fun-fact {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }

        .fun-fact::before {
            content: "💡";
            font-size: 2rem;
            position: absolute;
            top: 10px;
            left: 20px;
        }

        .fun-fact h3 {
            margin-right: 60px;
            margin-bottom: 10px;
        }

        .medical-icon {
            font-size: 3rem;
            color: #3498db;
            margin: 20px 0;
            text-align: center;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
            margin-top: 20px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            width: 0%;
            transition: width 0.3s ease;
        }

        .team-member {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: rgba(52, 152, 219, 0.1);
            border-radius: 10px;
        }

        .team-icon {
            font-size: 2rem;
            margin-left: 20px;
            color: #3498db;
        }

        .glossary-term {
            background: rgba(46, 204, 113, 0.1);
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            border-right: 4px solid #2ecc71;
        }

        .glossary-term h4 {
            color: #27ae60;
            margin-bottom: 10px;
        }

        .reading-time {
            color: #7f8c8d;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        @media (max-width: 768px) {
            .book-title {
                font-size: 2rem;
            }
            
            .navigation {
                flex-direction: column;
                align-items: center;
            }
            
            .nav-btn {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="book-header">
            <h1 class="book-title">رحلة داخل جسم الإنسان</h1>
            <p style="font-size: 1.3rem; color: #3498db; margin-bottom: 10px;">دليل مبسط للتصوير الطبي النووي</p>
            <div class="author-info">
                <p><strong>المؤلف:</strong> د. محمد يعقوب إسماعيل يعقوب</p>
                <p>نائب عميد كلية الهندسة - أستاذ مساعد الهندسة الطبية الحيوية</p>
                <p>جامعة السودان للعلوم والتكنولوجيا, © 2025</p>
            </div>
            <div class="book-description">
                هذا الكتاب هو تذكرتك في رحلة استكشافية مذهلة إلى عالم غير مرئي داخل جسمك. بأسلوب يشبه دليل السفر، سنكتشف معًا كيف يستخدم الأطباء "أضواءً بيولوجية" دقيقة لتتبع مسارات الأمراض ورؤية عمل أعضائنا بشكل مباشر.
            </div>
        </div>

        <div class="navigation">
            <button class="nav-btn active" onclick="showSection('part1')">الجزء الأول: الاستعداد للرحلة</button>
            <button class="nav-btn" onclick="showSection('part2')">الجزء الثاني: الانطلاق</button>
            <button class="nav-btn" onclick="showSection('part3')">الجزء الثالث: بطاقات بريدية</button>
            <button class="nav-btn" onclick="showSection('part4')">الجزء الرابع: فريق الرحلة</button>
            <button class="nav-btn" onclick="showSection('glossary')">فك الشفرة</button>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressBar"></div>
        </div>

        <!-- الجزء الأول -->
        <div id="part1" class="content-section active">
            <h2 class="section-title">الجزء الأول: الاستعداد للرحلة</h2>
            <p class="reading-time">⏱️ وقت القراءة المتوقع: 15 دقيقة</p>
            
            <div class="chapter">
                <h3 class="chapter-title" onclick="toggleChapter(this)">🧭 الفصل الأول: الـ GPS الطبي</h3>
                <div class="chapter-content">
                    <div class="medical-icon">🗺️</div>
                    <p>تخيل أننا نريد إرسال رسالة إلى خلية معينة في الجسم. كيف نضمن وصولها؟</p>
                    <p>الحل هو استخدام "متتبع طبي" يعمل مثل نظام تحديد المواقع العالمي (GPS). هذا المتتبع هو جزيء يعرف طريقه إلى العضو المستهدف، ونحن نرفق به "جهاز إرسال" صغير جداً (ذرة مشعة) لنتمكن من تتبعه.</p>
                    
                    <div class="fun-fact">
                        <h3>حقيقة مدهشة!</h3>
                        <p>يمكن للمتتبع الطبي أن يجد طريقه إلى خلية واحدة من بين تريليونات الخلايا في جسمك!</p>
                    </div>
                </div>
            </div>

            <div class="chapter">
                <h3 class="chapter-title" onclick="toggleChapter(this)">💡 الفصل الثاني: الضوء الخفي</h3>
                <div class="chapter-content">
                    <div class="medical-icon">☀️</div>
                    <p>هل الإشعاع مخيف حقاً؟ الجواب: لا! نحن نتعرض له كل يوم من الشمس والأرض وحتى من بعض الأطعمة مثل الموز!</p>
                    <p>كمية الإشعاع المستخدمة في الفحص تشبه مصباحاً خافتاً جداً يضيء لفترة قصيرة ثم ينطفئ من تلقاء نفسه.</p>
                    
                    <div class="fun-fact">
                        <h3>مقارنة عملية</h3>
                        <p>جرعة الفحص تعادل الإشعاع الذي تتعرض له في رحلة طيران عبر المحيط! ✈️</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- الجزء الثاني -->
        <div id="part2" class="content-section">
            <h2 class="section-title">الجزء الثاني: الانطلاق</h2>
            <p class="reading-time">⏱️ وقت القراءة المتوقع: 12 دقيقة</p>
            
            <div class="chapter">
                <h3 class="chapter-title" onclick="toggleChapter(this)">💉 الفصل الثالث: تذكرة الصعود</h3>
                <div class="chapter-content">
                    <div class="medical-icon">🎫</div>
                    <p>الحقنة أو الحبة هي تذكرة الصعود إلى رحلتنا داخل الجسم. لا تسبب أي شعور مختلف، فهي ليست صبغة ولا تسبب حساسية.</p>
                    <p><strong>فترة الانتظار:</strong> هي الفترة التي تستغرقها "مركباتنا الصغيرة" للوصول إلى وجهتها داخل الجسم.</p>
                </div>
            </div>

            <div class="chapter">
                <h3 class="chapter-title" onclick="toggleChapter(this)">📷 الفصل الرابع: الكاميرا السحرية</h3>
                <div class="chapter-content">
                    <div class="medical-icon">📸</div>
                    <p>أجهزة التصوير هي "كاميرات فائقة الحساسية" مصممة فقط لالتقاط الإشارات القادمة من الـ GPS الطبي.</p>
                    <p><strong>مهم:</strong> الجهاز لا يصدر أي إشعاع، بل يستقبل فقط!</p>
                    
                    <div class="fun-fact">
                        <h3>تجربة الفحص</h3>
                        <p>الاستلقاء بهدوء بينما تقوم الكاميرا برسم خريطة دقيقة لما يحدث في أعضائك 🗺️</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- الجزء الثالث -->
        <div id="part3" class="content-section">
            <h2 class="section-title">الجزء الثالث: بطاقات بريدية من الداخل</h2>
            <p class="reading-time">⏱️ وقت القراءة المتوقع: 20 دقيقة</p>
            
            <div class="chapter">
                <h3 class="chapter-title" onclick="toggleChapter(this)">🔍 الفصل الخامس: البحث عن الخلايا النشطة</h3>
                <div class="chapter-content">
                    <div class="medical-icon">🔬</div>
                    <p>الخلايا السرطانية "شرهة" وتستهلك السكر أكثر من الخلايا العادية. نستخدم هذه المعلومة لجعل الأورام "تضيء" في الصورة.</p>
                    
                    <div class="fun-fact">
                        <h3>كيف نكشف الأورام؟</h3>
                        <p>نعطي المريض سكر مشع، والخلايا السرطانية تأكل أكثر فتظهر أكثر إشراقاً! ✨</p>
                    </div>
                </div>
            </div>

            <div class="chapter">
                <h3 class="chapter-title" onclick="toggleChapter(this)">❤️ الفصل السادس: فحص محرك الجسم</h3>
                <div class="chapter-content">
                    <div class="medical-icon">🫀</div>
                    <p>عضلة القلب تشبه المحرك، والتصوير النووي يخبرنا ما إذا كان "الوقود" (الدم) يصل إلى جميع أجزاء المحرك بشكل جيد.</p>
                </div>
            </div>

            <div class="chapter">
                <h3 class="chapter-title" onclick="toggleChapter(this)">🧠 الفصل السابع: خريطة الأفكار</h3>
                <div class="chapter-content">
                    <div class="medical-icon">🧠</div>
                    <p>يمكن للتصوير النووي أن يظهر مناطق الدماغ التي لا تعمل بشكل جيد، مما يساعد في تشخيص أمراض مثل الزهايمر أو الصرع.</p>
                </div>
            </div>
        </div>

        <!-- الجزء الرابع -->
        <div id="part4" class="content-section">
            <h2 class="section-title">الجزء الرابع: فريق الرحلة</h2>
            <p class="reading-time">⏱️ وقت القراءة المتوقع: 10 دقائق</p>
            
            <div class="chapter">
                <h3 class="chapter-title" onclick="toggleChapter(this)">👨‍⚕️ الفصل الثامن: الطاقم الخفي</h3>
                <div class="chapter-content">
                    <div class="medical-icon">👥</div>
                    <p>هناك فريق كامل من الخبراء يعمل خلف الكواليس لضمان أن تكون رحلتك آمنة ونتائجها دقيقة:</p>
                    
                    <div class="team-member">
                        <div class="team-icon">🩺</div>
                        <div>
                            <h4>طبيب الطب النووي</h4>
                            <p>قائد الرحلة - يفسر النتائج ويضع التشخيص</p>
                        </div>
                    </div>
                    
                    <div class="team-member">
                        <div class="team-icon">🔧</div>
                        <div>
                            <h4>التقني المختص</h4>
                            <p>المرشد - يقوم بالفحص ويشغل الأجهزة</p>
                        </div>
                    </div>
                    
                    <div class="team-member">
                        <div class="team-icon">⚗️</div>
                        <div>
                            <h4>الفيزيائي الطبي</h4>
                            <p>مهندس السلامة - يضمن أن كل شيء آمن</p>
                        </div>
                    </div>
                    
                    <div class="team-member">
                        <div class="team-icon">💊</div>
                        <div>
                            <h4>الصيدلي النووي</h4>
                            <p>مُعدّ الـ GPS - يحضر المواد المشعة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- المسرد -->
        <div id="glossary" class="content-section">
            <h2 class="section-title">ملحق فك الشفرة</h2>
            <p class="reading-time">⏱️ وقت القراءة المتوقع: 8 دقائق</p>
            
            <div class="glossary-term">
                <h4>SPECT</h4>
                <p>تصوير مقطعي بانبعاث فوتون واحد - كاميرا تدور حول الجسم لتكوين صور ثلاثية الأبعاد</p>
            </div>
            
            <div class="glossary-term">
                <h4>PET</h4>
                <p>تصوير مقطعي بالإشعاع البوزيتروني - تقنية متقدمة لرؤية نشاط الخلايا</p>
            </div>
            
            <div class="glossary-term">
                <h4>PET/CT</h4>
                <p>دمج تصوير PET مع التصوير المقطعي المحوسب للحصول على صور تشريحية ووظيفية معاً</p>
            </div>
            
            <div class="glossary-term">
                <h4>كاميرا جاما</h4>
                <p>الجهاز الذي يلتقط الإشعاع المنبعث من الجسم ويحوله إلى صورة</p>
            </div>
            
            <div class="glossary-term">
                <h4>المتتبع المشع</h4>
                <p>المادة التي تحتوي على ذرة مشعة وتذهب إلى العضو المستهدف</p>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId) {
            // إخفاء جميع الأقسام
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });
            
            // إظهار القسم المطلوب
            document.getElementById(sectionId).classList.add('active');
            
            // تحديث الأزرار
            const buttons = document.querySelectorAll('.nav-btn');
            buttons.forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // تحديث شريط التقدم
            updateProgressBar(sectionId);
        }
        
        function toggleChapter(element) {
            const content = element.nextElementSibling;
            const isActive = content.classList.contains('active');
            
            // إغلاق جميع الفصول في القسم الحالي
            const currentSection = element.closest('.content-section');
            const allChapters = currentSection.querySelectorAll('.chapter-content');
            allChapters.forEach(chapter => {
                chapter.classList.remove('active');
            });
            
            // فتح الفصل المطلوب إذا لم يكن مفتوحاً
            if (!isActive) {
                content.classList.add('active');
            }
        }
        
        function updateProgressBar(sectionId) {
            const progressBar = document.getElementById('progressBar');
            const progress = {
                'part1': 25,
                'part2': 50,
                'part3': 75,
                'part4': 90,
                'glossary': 100
            };
            
            progressBar.style.width = progress[sectionId] + '%';
        }
        
        // تحديث شريط التقدم عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateProgressBar('part1');
        });
        
        // إضافة تأثيرات تفاعلية للفصول
        document.querySelectorAll('.chapter-title').forEach(title => {
            title.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(-10px)';
            });
            
            title.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
            });
        });
        
        // إضافة تأثير الكتابة المتحركة للعنوان
        const title = document.querySelector('.book-title');
        const titleText = title.textContent;
        title.textContent = '';
        
        let i = 0;
        function typeWriter() {
            if (i < titleText.length) {
                title.textContent += titleText.charAt(i);
                i++;
                setTimeout(typeWriter, 100);
            }
        }
        
        // بدء تأثير الكتابة عند تحميل الصفحة
        window.addEventListener('load', typeWriter);
    </script>
</body>
</html>