/* Landing Page Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    overflow-x: hidden;
    line-height: 1.6;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Loading Styles */
.loading-container {
    text-align: center;
    animation: fadeIn 0.5s ease-in;
}

.loading-content h1 {
    font-size: clamp(2rem, 5vw, 2.5rem);
    margin-bottom: 10px;
    font-weight: bold;
}

.loading-content p {
    margin-bottom: 30px;
    opacity: 0.9;
    font-size: clamp(1rem, 3vw, 1.1rem);
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s linear infinite;
    margin: 0 auto;
    will-change: transform;
}

/* Optimized Animations */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(20px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

@keyframes slideUp {
    from { 
        opacity: 0; 
        transform: translateY(30px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

/* Selection Container */
.selection-container {
    width: 100%;
    max-width: 900px;
    padding: 20px;
    animation: slideUp 0.6s ease-out;
}

.selection-content {
    text-align: center;
    background: rgba(255,255,255,0.1);
    -webkit-backdrop-filter: blur(15px);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    padding: 50px;
    border: 1px solid rgba(255,255,255,0.2);
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

/* Welcome Header */
.welcome-header h1 {
    font-size: clamp(2.2rem, 6vw, 2.8rem);
    margin-bottom: 15px;
    font-weight: bold;
}

.welcome-header h2 {
    font-size: clamp(1.6rem, 4vw, 2rem);
    margin-bottom: 15px;
    color: #ffd700;
    font-weight: 600;
}

.welcome-header p {
    margin-bottom: 10px;
    opacity: 0.9;
    font-size: clamp(1rem, 2.5vw, 1.1rem);
}

/* Options Grid */
.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 50px;
}

.option-card {
    background: rgba(255,255,255,0.15);
    border-radius: 20px;
    padding: 40px;
    text-decoration: none;
    color: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255,255,255,0.2);
    position: relative;
    overflow: hidden;
    display: block;
    will-change: transform;
}

.option-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.option-card:hover::before {
    opacity: 1;
}

.option-card:hover {
    transform: translateY(-8px) scale(1.02);
    background: rgba(255,255,255,0.25);
    box-shadow: 0 15px 40px rgba(0,0,0,0.4);
}

.option-card:focus {
    outline: 2px solid #ffd700;
    outline-offset: 2px;
}

.option-icon {
    margin-bottom: 25px;
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: center;
}

.option-card h3 {
    font-size: clamp(1.4rem, 3vw, 1.6rem);
    margin-bottom: 20px;
    font-weight: bold;
    position: relative;
    z-index: 1;
}

.option-card p {
    font-size: clamp(0.9rem, 2vw, 1rem);
    line-height: 1.6;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .options-grid {
        grid-template-columns: 1fr;
        gap: 25px;
        margin-top: 30px;
    }
    
    .selection-content {
        padding: 30px 20px;
    }
    
    .option-card {
        padding: 25px;
    }
}

@media (max-width: 480px) {
    .selection-content {
        padding: 20px 15px;
        border-radius: 15px;
    }
    
    .option-card {
        padding: 20px;
        border-radius: 15px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .option-card {
        border: 2px solid white;
    }
    
    .option-card:focus {
        outline: 3px solid #ffd700;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .loading-spinner {
        animation: none;
    }
}

/* Print Styles */
@media print {
    body {
        background: white;
        color: black;
    }
    
    .loading-container {
        display: none;
    }
    
    .selection-container {
        display: block !important;
    }
    
    .option-card {
        border: 1px solid black;
        background: white;
        color: black;
    }
}

/* Dark mode support (if system preference) */
@media (prefers-color-scheme: dark) {
    /* Already dark by default, but can add specific dark mode adjustments here */
}
