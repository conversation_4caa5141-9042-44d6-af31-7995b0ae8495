<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فيزياء العلاج الإشعاعي الحديث</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .book-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        .book-title {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .author-info {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            font-size: 1.1em;
        }

        .target-audience {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
        }

        .description {
            background: rgba(52, 152, 219, 0.1);
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #3498db;
            margin-bottom: 30px;
            font-size: 1.1em;
        }

        .navigation {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .nav-btn.active {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
        }

        .content-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 20px;
            display: none;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        .content-section.active {
            display: block;
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .part-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #3498db;
        }

        .chapter {
            background: rgba(52, 152, 219, 0.05);
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 15px;
            border-right: 4px solid #3498db;
            transition: all 0.3s ease;
        }

        .chapter:hover {
            transform: translateX(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .chapter-title {
            font-size: 1.3em;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .chapter-content {
            color: #555;
            line-height: 1.8;
        }

        .chapter-content ul {
            margin-right: 20px;
            margin-top: 10px;
        }

        .chapter-content li {
            margin-bottom: 8px;
            padding-right: 5px;
        }

        .search-container {
            margin-bottom: 30px;
            text-align: center;
        }

        .search-box {
            padding: 15px;
            border: 2px solid #3498db;
            border-radius: 25px;
            font-size: 1.1em;
            width: 60%;
            outline: none;
            transition: all 0.3s ease;
        }

        .search-box:focus {
            border-color: #2980b9;
            box-shadow: 0 0 15px rgba(52, 152, 219, 0.3);
        }

        .highlight {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
            padding: 2px 8px;
            border-radius: 15px;
            font-weight: bold;
        }

        .progress-bar {
            background: rgba(255, 255, 255, 0.3);
            height: 6px;
            border-radius: 3px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .feature-card {
            background: linear-gradient(135deg, rgba(142, 68, 173, 0.1), rgba(74, 144, 226, 0.1));
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(52, 152, 219, 0.2);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            font-size: 2em;
            margin-bottom: 15px;
            color: #3498db;
        }

        .footer {
            background: rgba(44, 62, 80, 0.9);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 15px;
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .book-title {
                font-size: 2em;
            }
            
            .search-box {
                width: 90%;
            }
            
            .navigation {
                flex-direction: column;
                align-items: center;
            }
            
            .nav-btn {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="progress-bar">
            <div class="progress-fill" id="progressBar"></div>
        </div>

        <div class="book-header">
            <h1 class="book-title">فيزياء العلاج الإشعاعي الحديث</h1>
            <div class="author-info">
                <strong>المؤلف:</strong> د. محمد يعقوب إسماعيل يعقوب<br>
                <strong>المنصب:</strong> نائب عميد كلية الهندسة - أستاذ مساعد الهندسة الطبية الحيوية<br>
                <strong>الجامعة:</strong> جامعة السودان للعلوم والتكنولوجيا<br>
                <strong>حقوق النشر:</strong> © 2025<br>
                <strong>البريد الإلكتروني:</strong> <EMAIL><br>
                <strong>الهاتف:</strong> +966538076790
            </div>
            <div class="target-audience">
                <strong>الجمهور المستهدف:</strong> الفيزيائيون الطبيون، أطباء الأورام المتخصصون في العلاج الإشعاعي، مخططو الجرعات، تقنيو العلاج الإشعاعي، وطلاب الدراسات العليا والباحثون في مجال فيزياء العلاج الإشعاعي
            </div>
        </div>

        <div class="search-container">
            <input type="text" class="search-box" id="searchBox" placeholder="ابحث في محتوى الكتاب...">
        </div>

        <div class="navigation">
            <button class="nav-btn active" onclick="showSection('overview')">نظرة عامة</button>
            <button class="nav-btn" onclick="showSection('part1')">الجزء الأول</button>
            <button class="nav-btn" onclick="showSection('part2')">الجزء الثاني</button>
            <button class="nav-btn" onclick="showSection('part3')">الجزء الثالث</button>
            <button class="nav-btn" onclick="showSection('part4')">الجزء الرابع</button>
            <button class="nav-btn" onclick="showSection('features')">الميزات</button>
        </div>

        <div class="content-section active" id="overview">
            <h2 class="part-title">وصف الكتاب</h2>
            <div class="description">
                مرجع علمي متقدم ومتخصص، يركز على الفيزياء الكامنة وراء أحدث تقنيات العلاج الإشعاعي. يغطي الكتاب بعمق التقنيات المتقدمة مثل العلاج الإشعاعي الموضعي (Brachytherapy) بأنواعه، والعلاج بالجسيمات (Proton/Carbon Therapy)، بالإضافة إلى التقنيات الفوتونية الحديثة كالعلاج الإشعاعي متغير الشدة (IMRT) والعلاج القوسي (VMAT). يولي الكتاب اهتمامًا خاصًا لفيزياء التخطيط العلاجي المعقد وقياس الجرعات الدقيق (Advanced Dosimetry) لهذه التقنيات، بهدف تزويد الممارسين والباحثين بالمعرفة الفيزيائية اللازمة لتطبيق هذه العلاجات بأمان وفعالية.
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🔬</div>
                    <h3>محتوى متقدم</h3>
                    <p>يغطي أحدث التقنيات في العلاج الإشعاعي مع التركيز على الأسس الفيزيائية</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>أمثلة عملية</h3>
                    <p>يحتوي على دراسات حالة ومعادلات محلولة لتطبيق المفاهيم النظرية</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <h3>تطبيق سريري</h3>
                    <p>يربط بين النظرية والممارسة العملية في البيئة السريرية</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔍</div>
                    <h3>ضمان الجودة</h3>
                    <p>يركز على أحدث بروتوكولات ضمان الجودة والسلامة</p>
                </div>
            </div>
        </div>

        <div class="content-section" id="part1">
            <h2 class="part-title">الجزء الأول: مراجعة أسس فيزياء العلاج الإشعاعي</h2>
            
            <div class="chapter">
                <h3 class="chapter-title">الفصل الأول: تفاعل الإشعاع مع الأنسجة</h3>
                <div class="chapter-content">
                    <ul>
                        <li>مراجعة تفاعلات الفوتونات والإلكترونات</li>
                        <li>مفاهيم الكيرما، الجرعة الممتصة، والاتزان الإلكتروني</li>
                        <li>منحنيات الجرعة العميقة (PDD)</li>
                        <li>نسبة الأنسجة للهواء (TAR)</li>
                        <li>نسبة الأنسجة للـ Phantom (TPR)</li>
                    </ul>
                </div>
            </div>

            <div class="chapter">
                <h3 class="chapter-title">الفصل الثاني: فيزياء المسرعات الخطية (LINACs)</h3>
                <div class="chapter-content">
                    <ul>
                        <li>توليد حزم الفوتونات والإلكترونات عالية الطاقة</li>
                        <li>أنظمة التوجيه والتشكيل (Bending Magnet, Flattening Filter, Scattering Foils)</li>
                        <li>الموجهات متعددة الشرائح (MLCs) ودورها في تشكيل الحزمة</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="content-section" id="part2">
            <h2 class="part-title">الجزء الثاني: فيزياء العلاج الإشعاعي الموضعي (Brachytherapy)</h2>
            
            <div class="chapter">
                <h3 class="chapter-title">الفصل الثالث: مصادر العلاج الإشعاعي الموضعي وقياس الجرعات</h3>
                <div class="chapter-content">
                    <ul>
                        <li>خصائص المصادر المشعة المستخدمة (Ir-192, Co-60, I-125)</li>
                        <li>معايرة المصادر وقياس قوة الكيرما في الهواء (Air Kerma Strength)</li>
                        <li>بروتوكول قياس الجرعات (AAPM TG-43): المبادئ والمعادلات</li>
                    </ul>
                </div>
            </div>

            <div class="chapter">
                <h3 class="chapter-title">الفصل الرابع: تقنيات التخطيط والتوصيل</h3>
                <div class="chapter-content">
                    <ul>
                        <li>العلاج بمعدل جرعة عالٍ (HDR) ومنخفض (LDR)</li>
                        <li>فيزياء أجهزة التحميل اللاحق (Afterloaders)</li>
                        <li>التخطيط العلاجي القائم على الصور ثلاثية الأبعاد</li>
                        <li>ضمان الجودة في العلاج الإشعاعي الموضعي</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="content-section" id="part3">
            <h2 class="part-title">الجزء الثالث: فيزياء العلاج بالجسيمات المشحونة</h2>
            
            <div class="chapter">
                <h3 class="chapter-title">الفصل الخامس: المبادئ الفيزيائية للعلاج بالبروتونات والأيونات الثقيلة</h3>
                <div class="chapter-content">
                    <ul>
                        <li>تفاعل الجسيمات المشحونة مع المادة</li>
                        <li><span class="highlight">ذروة براج (The Bragg Peak)</span> وميزاتها الفيزيائية</li>
                        <li>مفاهيم التشتت الجانبي وتجزئة الحزمة</li>
                        <li>الفعالية البيولوجية النسبية (RBE) وأهميتها في العلاج بالأيونات</li>
                    </ul>
                </div>
            </div>

            <div class="chapter">
                <h3 class="chapter-title">الفصل السادس: أنظمة إنتاج وتوصيل حزم الجسيمات</h3>
                <div class="chapter-content">
                    <ul>
                        <li>فيزياء المسرعات الحلقية (Cyclotrons) والسينكروترونات (Synchrotrons)</li>
                        <li>أنظمة توصيل الحزمة: التشتت السلبي (Passive Scattering) والمسح بالقلم الرصاص (Pencil Beam Scanning)</li>
                    </ul>
                </div>
            </div>

            <div class="chapter">
                <h3 class="chapter-title">الفصل السابع: التخطيط العلاجي وقياس الجرعات في العلاج بالبروتونات</h3>
                <div class="chapter-content">
                    <ul>
                        <li>التخطيط العلاجي باستخدام شدة موحدة (SFUD) ومتغيرة (IMPT)</li>
                        <li>إدارة عدم اليقين في المدى (Range Uncertainty)</li>
                        <li>قياس الجرعات المطلقة والنسبية لحزم البروتونات</li>
                        <li>مقدمة في فيزياء العلاج بأيونات الكربون</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="content-section" id="part4">
            <h2 class="part-title">الجزء الرابع: قياس الجرعات المتقدم وضمان الجودة</h2>
            
            <div class="chapter">
                <h3 class="chapter-title">الفصل الثامن: فيزياء وقياس جرعات الحقول الصغيرة</h3>
                <div class="chapter-content">
                    <ul>
                        <li>تحديات قياس الجرعات في العلاج الإشعاعي التجسيمي (SRS/SBRT)</li>
                        <li>فقدان الاتزان الإلكتروني الجانبي</li>
                        <li>اختيار الكواشف المناسبة (غرف التأين الدقيقة، الدايود، الأفلام)</li>
                    </ul>
                </div>
            </div>

            <div class="chapter">
                <h3 class="chapter-title">الفصل التاسع: ضمان الجودة للتقنيات المتقدمة</h3>
                <div class="chapter-content">
                    <ul>
                        <li>ضمان الجودة الخاص بالـ <span class="highlight">MLCs</span></li>
                        <li>ضمان الجودة الخاص بالمريض (Patient-Specific QA) لخطط IMRT و VMAT</li>
                        <li>استخدام الفانتومات والأجهزة المتقدمة في التحقق من الجرعة</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="content-section" id="features">
            <h2 class="part-title">متطلبات الكتاب والميزات الخاصة</h2>
            
            <div class="chapter">
                <h3 class="chapter-title">الرسومات والأشكال</h3>
                <div class="chapter-content">
                    <ul>
                        <li>مقارنة بيانية بين منحنى الجرعة العميقة للفوتونات وذروة براج للبروتونات</li>
                        <li>رسومات تخطيطية للمسرعات الخطية والحلقية وأجهزة التحميل اللاحق</li>
                        <li>خرائط توزيع الجرعة توضح الفرق بين تقنيات العلاج المختلفة</li>
                        <li>صور توضيحية للكواشف والفانتومات المستخدمة</li>
                    </ul>
                </div>
            </div>

            <div class="chapter">
                <h3 class="chapter-title">الجداول والمعادلات</h3>
                <div class="chapter-content">
                    <ul>
                        <li>جداول مقارنة بين تقنيات العلاج الإشعاعي المختلفة</li>
                        <li>معادلات بروتوكول TG-43 مع شرح مفصل</li>
                        <li>معادلات حساب وحدات المراقبة للتقنيات المتقدمة</li>
                        <li>أمثلة عملية محلولة لحساب الجرعات</li>
                    </ul>
                </div>
            </div>

            <div class="chapter">
                <h3 class="chapter-title">دراسات الحالة</h3>
                <div class="chapter-content">
                    <ul>
                        <li>حالة بروستاتا بتقنية IMRT</li>
                        <li>ورم دماغي بتقنية SRS</li>
                        <li>العلاج الإشعاعي الموجه بالصور (IGRT)</li>
                        <li>التقارير والبروتوكولات الدولية (AAPM, ICRU, ESTRO)</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>&copy; 2025 د. محمد يعقوب إسماعيل يعقوب - جميع الحقوق محفوظة</p>
            <p>مرجع علمي متخصص في فيزياء العلاج الإشعاعي الحديث</p>
        </div>
    </div>

    <script>
        // Navigation functionality
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Remove active class from all nav buttons
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionId).classList.add('active');
            
            // Add active class to clicked button
            event.target.classList.add('active');
            
            // Update progress bar
            updateProgressBar();
        }

        // Search functionality
        document.getElementById('searchBox').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const chapters = document.querySelectorAll('.chapter');
            
            chapters.forEach(chapter => {
                const content = chapter.textContent.toLowerCase();
                if (content.includes(searchTerm) || searchTerm === '') {
                    chapter.style.display = 'block';
                    chapter.style.opacity = '1';
                } else {
                    chapter.style.display = 'none';
                    chapter.style.opacity = '0.5';
                }
            });
        });

        // Progress bar functionality
        function updateProgressBar() {
            const sections = ['overview', 'part1', 'part2', 'part3', 'part4', 'features'];
            const activeSection = document.querySelector('.content-section.active').id;
            const currentIndex = sections.indexOf(activeSection);
            const progress = ((currentIndex + 1) / sections.length) * 100;
            
            document.getElementById('progressBar').style.width = progress + '%';
        }

        // Smooth scrolling and animations
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize progress bar
            updateProgressBar();
            
            // Add hover effects to chapters
            document.querySelectorAll('.chapter').forEach(chapter => {
                chapter.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(-10px)';
                });
                
                chapter.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                });
            });
            
            // Add typing effect to search placeholder
            const searchBox = document.getElementById('searchBox');
            const originalPlaceholder = searchBox.placeholder;
            
            searchBox.addEventListener('focus', function() {
                this.placeholder = '';
            });
            
            searchBox.addEventListener('blur', function() {
                if (this.value === '') {
                    this.placeholder = originalPlaceholder;
                }
            });
        });

        // Keyboard navigation
        document.addEventListener('keydown', function(event) {
            if (event.ctrlKey && event.key >= '1' && event.key <= '6') {
                event.preventDefault();
                const sections = ['overview', 'part1', 'part2', 'part3', 'part4', 'features'];
                const index = parseInt(event.key) - 1;
                if (index < sections.length) {
                    showSection(sections[index]);
                }
            }
        });

        // Auto-save reading progress (simulated)
        let readingProgress = {
            currentSection: 'overview',
            timeSpent: 0,
            lastVisited: new Date().toISOString()
        };

        setInterval(function() {
            readingProgress.timeSpent += 1;
            readingProgress.lastVisited = new Date().toISOString();
            readingProgress.currentSection = document.querySelector('.content-section.active').id;
            
            // In a real application, this would be saved to a database
            console.log('Reading progress updated:', readingProgress);
        }, 60000); // Update every minute
    </script>
</body>
</html>