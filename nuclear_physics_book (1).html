<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أساسيات الفيزياء الطبية النووية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: fadeInUp 0.8s ease-out;
        }

        .book-title {
            font-size: 2.5em;
            color: #fff;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .author-info {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 15px;
            margin-top: 20px;
            color: #333;
        }

        .author-name {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .contact-info {
            font-size: 0.9em;
            color: #666;
        }

        /* Navigation */
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .nav-tab {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1em;
            backdrop-filter: blur(5px);
        }

        .nav-tab:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .nav-tab.active {
            background: #fff;
            color: #333;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        /* Content sections */
        .content-section {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            animation: fadeIn 0.5s ease-out;
        }

        .content-section.active {
            display: block;
        }

        .section-title {
            font-size: 2em;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        /* Book structure */
        .book-part {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border-left: 5px solid #667eea;
            transition: transform 0.3s ease;
        }

        .book-part:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .part-title {
            font-size: 1.5em;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .chapter {
            background: #fff;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border: 1px solid #e9ecef;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .chapter:hover {
            background: #f1f3f4;
            border-color: #667eea;
            transform: translateX(10px);
        }

        .chapter-title {
            font-size: 1.2em;
            color: #495057;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .chapter-content {
            display: none;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }

        .chapter-content.show {
            display: block;
            animation: slideDown 0.3s ease-out;
        }

        .chapter-topics {
            list-style: none;
            padding-right: 20px;
        }

        .chapter-topics li {
            padding: 5px 0;
            color: #666;
            position: relative;
        }

        .chapter-topics li::before {
            content: '◆';
            color: #667eea;
            margin-left: 10px;
        }

        /* Features grid */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            transform: translateY(0);
            transition: transform 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .feature-title {
            font-size: 1.3em;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .feature-description {
            font-size: 0.9em;
            line-height: 1.6;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
            }
            to {
                opacity: 1;
                max-height: 500px;
            }
        }

        /* Target audience */
        .audience-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .audience-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .audience-item:hover {
            border-color: #667eea;
            background: #fff;
            transform: translateY(-2px);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .book-title {
                font-size: 2em;
            }

            .nav-tabs {
                flex-direction: column;
                align-items: center;
            }

            .nav-tab {
                width: 200px;
                text-align: center;
            }

            .header {
                padding: 20px;
            }

            .content-section {
                padding: 20px;
            }
        }

        /* Floating particles background */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="particles" id="particles"></div>

    <div class="container">
        <header class="header">
            <h1 class="book-title">أساسيات الفيزياء الطبية النووية</h1>
            <div class="author-info">
                <div class="author-name">د. محمد يعقوب إسماعيل يعقوب</div>
                <div class="contact-info">
                    نائب عميد كلية الهندسة - أستاذ مساعد الهندسة الطبية الحيوية<br>
                    جامعة السودان للعلوم والتكنولوجيا<br>
                    البريد الإلكتروني: <EMAIL><br>
                    الهاتف: +966538076790
                </div>
            </div>
        </header>

        <nav class="nav-tabs">
            <button class="nav-tab active" onclick="showSection('overview')">نظرة عامة</button>
            <button class="nav-tab" onclick="showSection('structure')">هيكل الكتاب</button>
            <button class="nav-tab" onclick="showSection('features')">الميزات</button>
            <button class="nav-tab" onclick="showSection('audience')">الجمهور المستهدف</button>
        </nav>

        <main>
            <section id="overview" class="content-section active">
                <h2 class="section-title">وصف الكتاب</h2>
                <p style="font-size: 1.1em; line-height: 1.8; text-align: justify; margin-bottom: 20px;">
                    كتاب منهجي مصمم ليكون المدخل الأساسي والشامل لعلم الفيزياء الطبية النووية. يغطي الكتاب المبادئ الفيزيائية الجوهرية بدءًا من التركيب الذري والنشاط الإشعاعي، مرورًا بأنواع التفاعلات النووية وتفاعلات الإشعاع مع المادة، وصولًا إلى شرح مفصل لفيزياء وآليات عمل أجهزة الكشف والقياس الإشعاعي المستخدمة في البيئة الطبية.
                </p>
                <p style="font-size: 1.1em; line-height: 1.8; text-align: justify;">
                    يهدف الكتاب إلى بناء قاعدة علمية صلبة لدى الطالب، مع التركيز على المفاهيم الأساسية وتوضيحها من خلال رسومات دقيقة، وجداول مقارنة، ومعادلات فيزيائية أساسية مع أمثلة تطبيقية.
                </p>
            </section>

            <section id="structure" class="content-section">
                <h2 class="section-title">هيكل الكتاب</h2>
                
                <div class="book-part">
                    <h3 class="part-title">الجزء الأول: فيزياء الذرة والنواة</h3>
                    
                    <div class="chapter" onclick="toggleChapter(this)">
                        <h4 class="chapter-title">الفصل الأول: التركيب الذري والنووي</h4>
                        <div class="chapter-content">
                            <ul class="chapter-topics">
                                <li>مقدمة تاريخية: من دالتون إلى النموذج الكمي</li>
                                <li>مكونات الذرة (الإلكترونات، البروتونات، النيوترونات)</li>
                                <li>النماذج الذرية (بور، النموذج الكمي)</li>
                                <li>تركيب النواة: القوى النووية، طاقة الترابط النووي</li>
                                <li>المصطلحات الأساسية: العدد الذري، العدد الكتلي، النظائر، المتماكبات</li>
                                <li>مخطط النيوكليدات (Chart of Nuclides)</li>
                            </ul>
                        </div>
                    </div>

                    <div class="chapter" onclick="toggleChapter(this)">
                        <h4 class="chapter-title">الفصل الثاني: النشاط الإشعاعي والتحلل النووي</h4>
                        <div class="chapter-content">
                            <ul class="chapter-topics">
                                <li>مفهوم الاستقرار النووي</li>
                                <li>قانون التحلل الإشعاعي: عمر النصف، ثابت التحلل، والنشاطية</li>
                                <li>أنواع التحلل الإشعاعي: ألفا، بيتا، جاما</li>
                                <li>مخططات التحلل (Decay Schemes)</li>
                                <li>الاتزان الإشعاعي (Secular and Transient Equilibrium)</li>
                            </ul>
                        </div>
                    </div>

                    <div class="chapter" onclick="toggleChapter(this)">
                        <h4 class="chapter-title">الفصل الثالث: تفاعل الإشعاع مع المادة</h4>
                        <div class="chapter-content">
                            <ul class="chapter-topics">
                                <li>تفاعل الجسيمات المشحونة مع المادة</li>
                                <li>التأثير الكهروضوئي (Photoelectric Effect)</li>
                                <li>تبعثر كومبتون (Compton Scattering)</li>
                                <li>إنتاج الزوج (Pair Production)</li>
                                <li>معاملات التوهين وطبقة النصف</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="book-part">
                    <h3 class="part-title">الجزء الثاني: إنتاج وقياس الإشعاع</h3>
                    
                    <div class="chapter" onclick="toggleChapter(this)">
                        <h4 class="chapter-title">الفصل الرابع: إنتاج النويدات المشعة</h4>
                        <div class="chapter-content">
                            <ul class="chapter-topics">
                                <li>التفاعلات النووية</li>
                                <li>إنتاج النويدات في المفاعلات النووية</li>
                                <li>إنتاج النويدات في المسرعات الحلقية</li>
                                <li>مولدات النويدات المشعة (التركيز على ⁹⁹Mo/⁹⁹ᵐTc)</li>
                            </ul>
                        </div>
                    </div>

                    <div class="chapter" onclick="toggleChapter(this)">
                        <h4 class="chapter-title">الفصل الخامس: مبادئ الكشف الإشعاعي</h4>
                        <div class="chapter-content">
                            <ul class="chapter-topics">
                                <li>الخصائص العامة للكواشف</li>
                                <li>الكفاءة وزمن الميت</li>
                                <li>الدقة الطاقية</li>
                                <li>أنواع الكواشف وتصنيفاتها</li>
                            </ul>
                        </div>
                    </div>

                    <div class="chapter" onclick="toggleChapter(this)">
                        <h4 class="chapter-title">الفصل السادس: الكواشف المليئة بالغاز</h4>
                        <div class="chapter-content">
                            <ul class="chapter-topics">
                                <li>مبدأ العمل: التأين وتجميع الشحنات</li>
                                <li>غرف التأين (Ionization Chambers)</li>
                                <li>العدادات التناسبية</li>
                                <li>عدادات جايجر-مولر</li>
                            </ul>
                        </div>
                    </div>

                    <div class="chapter" onclick="toggleChapter(this)">
                        <h4 class="chapter-title">الفصل السابع: الكواشف الوميضية</h4>
                        <div class="chapter-content">
                            <ul class="chapter-topics">
                                <li>مبدأ الوميض</li>
                                <li>البلورة الوميضية وأنبوب التضخيم الضوئي</li>
                                <li>أنواع المواد الوميضية</li>
                                <li>تحليل الطيف الطاقي</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="book-part">
                    <h3 class="part-title">الجزء الثالث: تطبيقات القياس والحماية</h3>
                    
                    <div class="chapter" onclick="toggleChapter(this)">
                        <h4 class="chapter-title">الفصل الثامن: قياس الجرعات الإشعاعية</h4>
                        <div class="chapter-content">
                            <ul class="chapter-topics">
                                <li>مقدمة في قياس الجرعات</li>
                                <li>الوحدات الإشعاعية المختلفة</li>
                                <li>أجهزة قياس الجرعات الشخصية</li>
                                <li>معايرة الأجهزة الإشعاعية</li>
                            </ul>
                        </div>
                    </div>

                    <div class="chapter" onclick="toggleChapter(this)">
                        <h4 class="chapter-title">الفصل التاسع: الحماية من الإشعاع</h4>
                        <div class="chapter-content">
                            <ul class="chapter-topics">
                                <li>المبادئ الأساسية للحماية الإشعاعية (ALARA)</li>
                                <li>حسابات التدريع</li>
                                <li>الحدود الجرعية للعاملين والجمهور</li>
                                <li>إدارة النفايات المشعة في المستشفيات</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <section id="features" class="content-section">
                <h2 class="section-title">ميزات الكتاب</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <h3 class="feature-title">رسومات وأشكال توضيحية</h3>
                        <p class="feature-description">رسومات تخطيطية واضحة للنماذج الذرية ومخططات التحلل الإشعاعي، مع رسوم بيانية لمنحنيات الكواشف وأطياف الطاقة</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📋</div>
                        <h3 class="feature-title">جداول مقارنة شاملة</h3>
                        <p class="feature-description">جداول مقارنة بين أنواع التحلل الإشعاعي والكواشف المختلفة، مع جداول للثوابت الفيزيائية والوحدات الإشعاعية</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔬</div>
                        <h3 class="feature-title">معادلات مع أمثلة تطبيقية</h3>
                        <p class="feature-description">معادلات فيزيائية أساسية مع شرح مفصل لكل رمز وأمثلة عددية محلولة خطوة بخطوة</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎯</div>
                        <h3 class="feature-title">أسلوب منهجي تعليمي</h3>
                        <p class="feature-description">كل فصل يبدأ بأهدافه التعليمية وينتهي بملخص وأسئلة للمراجعة ومسائل تدريبية</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🌐</div>
                        <h3 class="feature-title">توافق مع المعايير الدولية</h3>
                        <p class="feature-description">جميع المعلومات تتوافق مع أحدث توصيات الهيئات الدولية مثل ICRP و IAEA</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📚</div>
                        <h3 class="feature-title">ملاحق مفيدة</h3>
                        <p class="feature-description">ملحق بالمصطلحات العلمية (عربي - إنجليزي) وآخر بالثوابت الفيزيائية الهامة</p>
                    </div>
                </div>
            </section>

            <section id="audience" class="content-section">
                <h2 class="section-title">الجمهور المستهدف</h2>
                <div class="audience-list">
                    <div class="audience-item">
                        <h3>🎓 طلاب الفيزياء الطبية</h3>
                        <p>طلاب المرحلة الجامعية في تخصص الفيزياء الطبية</p>
                    </div>
                    <div class="audience-item">
                        <h3>⚕️ طلاب الهندسة الطبية</h3>
                        <p>طلاب الهندسة الطبية الحيوية وعلوم الأشعة</p>
                    </div>
                    <div class="audience-item">
                        <h3>🔬 التقنيون والفنيون</h3>
                        <p>التقنيون والفنيون الذين يبدأون مسيرتهم المهنية في أقسام الطب النووي</p>
                    </div>
                    <div class="audience-item">
                        <h3>👨‍⚕️ العاملون في المجال الطبي</h3>
                        <p>العاملون في أقسام الطب النووي والأشعة الطبية</p>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script>
        function showSection(sectionId) {
            // Hide all sections
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected section
            document.getElementById(sectionId).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        function toggleChapter(chapterElement) {
            const content = chapterElement.querySelector('.chapter-content');
            content.classList.toggle('show');
        }

        // Create floating particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                const size = Math.random() * 4 + 2;
                const posX = Math.random() * window.innerWidth;
                const posY = Math.random() * window.innerHeight;
                const duration = Math.random() * 3 + 3;
                const delay = Math.random() * 2;

                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;
                particle.style.left = `${posX}px`;
                particle.style.top = `${posY}px`;
                particle.style.animationDuration = `${duration}s`;
                particle.style.animationDelay = `${delay}s`;

                particlesContainer.appendChild(particle);
            }
        }

        // Initialize particles on page load
        window.addEventListener('load', createParticles);

        // Add smooth scrolling for better user experience
        document.addEventListener('DOMContentLoaded', function() {
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetSection = e.target.getAttribute('onclick').match(/'([^']+)'/)[1];
                    showSection(targetSection);
                });
            });
        });
    </script>
</body>
</html>