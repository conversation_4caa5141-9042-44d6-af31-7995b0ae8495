/* Critical CSS for above-the-fold content */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    overflow-x: hidden;
    line-height: 1.6;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Loading Styles */
.loading-container {
    text-align: center;
    animation: fadeIn 0.5s ease-in;
}

.loading-content h1 {
    font-size: clamp(2rem, 5vw, 2.5rem);
    margin-bottom: 10px;
    font-weight: bold;
}

.loading-content p {
    margin-bottom: 30px;
    opacity: 0.9;
    font-size: clamp(1rem, 3vw, 1.1rem);
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s linear infinite;
    margin: 0 auto;
    will-change: transform;
}

/* Critical Animations */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(20px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

/* Selection Container - Critical */
.selection-container {
    width: 100%;
    max-width: 900px;
    padding: 20px;
}

.selection-content {
    text-align: center;
    background: rgba(255,255,255,0.1);
    -webkit-backdrop-filter: blur(15px);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    padding: 50px;
    border: 1px solid rgba(255,255,255,0.2);
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

/* Welcome Header - Critical */
.welcome-header h1 {
    font-size: clamp(2.2rem, 6vw, 2.8rem);
    margin-bottom: 15px;
    font-weight: bold;
}

.welcome-header h2 {
    font-size: clamp(1.6rem, 4vw, 2rem);
    margin-bottom: 15px;
    color: #ffd700;
    font-weight: 600;
}

.welcome-header p {
    margin-bottom: 10px;
    opacity: 0.9;
    font-size: clamp(1rem, 2.5vw, 1.1rem);
}
