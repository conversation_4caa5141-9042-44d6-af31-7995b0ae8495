@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500&display=swap');

@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400&display=swap');

*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: Inter, sans-serif;
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: JetBrains Mono, monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

body {
  font-family: 'Inter', sans-serif;
  color: var(--color-text-primary);
  background-color: var(--color-background);
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Cairo', sans-serif;
  font-weight: 600;
  color: var(--color-text-primary);
}

.font-mono {
  font-family: 'JetBrains Mono', monospace;
}

.btn-primary{
  border-radius: 0.5rem;
  --tw-bg-opacity: 1;
  background-color: rgb(0 68 102 / var(--tw-bg-opacity, 1));
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-family: Cairo, sans-serif;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.btn-primary:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(0 51 85 / var(--tw-bg-opacity, 1));
}

.btn-primary:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(128 196 255 / var(--tw-ring-opacity, 1));
}

.btn-secondary{
  border-radius: 0.5rem;
  --tw-bg-opacity: 1;
  background-color: rgb(0 119 204 / var(--tw-bg-opacity, 1));
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-family: Cairo, sans-serif;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.btn-secondary:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(0 85 153 / var(--tw-bg-opacity, 1));
}

.btn-secondary:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(128 196 255 / var(--tw-ring-opacity, 1));
}

.card{
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 1.5rem;
  --tw-shadow: 0 4px 12px rgba(0, 68, 102, 0.1);
  --tw-shadow-colored: 0 4px 12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  box-shadow: var(--shadow-soft);
}

.card:hover{
  --tw-shadow: 0 8px 24px rgba(0, 68, 102, 0.15);
  --tw-shadow-colored: 0 8px 24px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  box-shadow: var(--shadow-medium);
}

.input-field{
  width: 100%;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

.input-field:focus{
  --tw-border-opacity: 1;
  border-color: rgb(0 68 102 / var(--tw-border-opacity, 1));
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(224 242 255 / var(--tw-ring-opacity, 1));
}

.static{
  position: static;
}

.fixed{
  position: fixed;
}

.absolute{
  position: absolute;
}

.relative{
  position: relative;
}

.sticky{
  position: sticky;
}

.inset-0{
  inset: 0px;
}

.-bottom-4{
  bottom: -1rem;
}

.-right-4{
  right: -1rem;
}

.bottom-6{
  bottom: 1.5rem;
}

.left-0{
  left: 0px;
}

.left-3{
  left: 0.75rem;
}

.left-4{
  left: 1rem;
}

.left-6{
  left: 1.5rem;
}

.right-0{
  right: 0px;
}

.right-1\/2{
  right: 50%;
}

.right-6{
  right: 1.5rem;
}

.top-0{
  top: 0px;
}

.top-1\/2{
  top: 50%;
}

.z-50{
  z-index: 50;
}

.order-1{
  order: 1;
}

.order-2{
  order: 2;
}

.mx-2{
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-auto{
  margin-left: auto;
  margin-right: auto;
}

.mb-1{
  margin-bottom: 0.25rem;
}

.mb-12{
  margin-bottom: 3rem;
}

.mb-16{
  margin-bottom: 4rem;
}

.mb-2{
  margin-bottom: 0.5rem;
}

.mb-3{
  margin-bottom: 0.75rem;
}

.mb-4{
  margin-bottom: 1rem;
}

.mb-6{
  margin-bottom: 1.5rem;
}

.mb-8{
  margin-bottom: 2rem;
}

.ml-1{
  margin-left: 0.25rem;
}

.ml-2{
  margin-left: 0.5rem;
}

.ml-3{
  margin-left: 0.75rem;
}

.ml-4{
  margin-left: 1rem;
}

.mr-10{
  margin-right: 2.5rem;
}

.mr-2{
  margin-right: 0.5rem;
}

.mr-4{
  margin-right: 1rem;
}

.mt-1{
  margin-top: 0.25rem;
}

.mt-2{
  margin-top: 0.5rem;
}

.mt-3{
  margin-top: 0.75rem;
}

.mt-4{
  margin-top: 1rem;
}

.mt-6{
  margin-top: 1.5rem;
}

.mt-8{
  margin-top: 2rem;
}

.block{
  display: block;
}

.inline-block{
  display: inline-block;
}

.flex{
  display: flex;
}

.grid{
  display: grid;
}

.hidden{
  display: none;
}

.h-10{
  height: 2.5rem;
}

.h-12{
  height: 3rem;
}

.h-16{
  height: 4rem;
}

.h-2{
  height: 0.5rem;
}

.h-20{
  height: 5rem;
}

.h-3{
  height: 0.75rem;
}

.h-4{
  height: 1rem;
}

.h-48{
  height: 12rem;
}

.h-5{
  height: 1.25rem;
}

.h-6{
  height: 1.5rem;
}

.h-64{
  height: 16rem;
}

.h-8{
  height: 2rem;
}

.h-80{
  height: 20rem;
}

.h-96{
  height: 24rem;
}

.h-full{
  height: 100%;
}

.h-screen{
  height: 100vh;
}

.min-h-screen{
  min-height: 100vh;
}

.w-1{
  width: 0.25rem;
}

.w-10{
  width: 2.5rem;
}

.w-12{
  width: 3rem;
}

.w-16{
  width: 4rem;
}

.w-2{
  width: 0.5rem;
}

.w-24{
  width: 6rem;
}

.w-3{
  width: 0.75rem;
}

.w-32{
  width: 8rem;
}

.w-4{
  width: 1rem;
}

.w-5{
  width: 1.25rem;
}

.w-6{
  width: 1.5rem;
}

.w-8{
  width: 2rem;
}

.w-80{
  width: 20rem;
}

.w-96{
  width: 24rem;
}

.w-full{
  width: 100%;
}

.min-w-40{
  min-width: 10rem;
}

.max-w-2xl{
  max-width: 42rem;
}

.max-w-3xl{
  max-width: 48rem;
}

.max-w-4xl{
  max-width: 56rem;
}

.max-w-7xl{
  max-width: 80rem;
}

.max-w-md{
  max-width: 28rem;
}

.max-w-none{
  max-width: none;
}

.max-w-sm{
  max-width: 24rem;
}

.flex-1{
  flex: 1 1 0%;
}

.flex-shrink-0{
  flex-shrink: 0;
}

.-translate-x-1\/2{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-1\/2{
  --tw-translate-x: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform-gpu{
  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.cursor-pointer{
  cursor: pointer;
}

.list-inside{
  list-style-position: inside;
}

.list-disc{
  list-style-type: disc;
}

.grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2{
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-4{
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.flex-col{
  flex-direction: column;
}

.flex-wrap{
  flex-wrap: wrap;
}

.items-start{
  align-items: flex-start;
}

.items-center{
  align-items: center;
}

.items-baseline{
  align-items: baseline;
}

.justify-end{
  justify-content: flex-end;
}

.justify-center{
  justify-content: center;
}

.justify-between{
  justify-content: space-between;
}

.gap-12{
  gap: 3rem;
}

.gap-2{
  gap: 0.5rem;
}

.gap-3{
  gap: 0.75rem;
}

.gap-4{
  gap: 1rem;
}

.gap-6{
  gap: 1.5rem;
}

.gap-8{
  gap: 2rem;
}

.space-x-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.space-y-12 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(3rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.space-x-reverse > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 1;
}

.overflow-hidden{
  overflow: hidden;
}

.overflow-y-auto{
  overflow-y: auto;
}

.rounded{
  border-radius: 0.25rem;
}

.rounded-2xl{
  border-radius: 1rem;
}

.rounded-full{
  border-radius: 9999px;
}

.rounded-lg{
  border-radius: 0.5rem;
}

.rounded-md{
  border-radius: 0.375rem;
}

.rounded-r-lg{
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

.border{
  border-width: 1px;
}

.border-2{
  border-width: 2px;
}

.border-4{
  border-width: 4px;
}

.border-b{
  border-bottom-width: 1px;
}

.border-l{
  border-left-width: 1px;
}

.border-r{
  border-right-width: 1px;
}

.border-r-4{
  border-right-width: 4px;
}

.border-t{
  border-top-width: 1px;
}

.border-accent-200{
  --tw-border-opacity: 1;
  border-color: rgb(255 214 204 / var(--tw-border-opacity, 1));
}

.border-border{
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.border-primary{
  --tw-border-opacity: 1;
  border-color: rgb(0 68 102 / var(--tw-border-opacity, 1));
}

.border-primary-200{
  --tw-border-opacity: 1;
  border-color: rgb(179 217 255 / var(--tw-border-opacity, 1));
}

.border-primary-600{
  --tw-border-opacity: 1;
  border-color: rgb(0 102 204 / var(--tw-border-opacity, 1));
}

.border-secondary-200{
  --tw-border-opacity: 1;
  border-color: rgb(179 217 255 / var(--tw-border-opacity, 1));
}

.border-success-200{
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}

.border-success-300{
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));
}

.border-surface{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.border-warning-200{
  --tw-border-opacity: 1;
  border-color: rgb(253 230 138 / var(--tw-border-opacity, 1));
}

.bg-accent{
  --tw-bg-opacity: 1;
  background-color: rgb(255 107 53 / var(--tw-bg-opacity, 1));
}

.bg-accent-100{
  --tw-bg-opacity: 1;
  background-color: rgb(255 235 229 / var(--tw-bg-opacity, 1));
}

.bg-accent-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 245 242 / var(--tw-bg-opacity, 1));
}

.bg-background{
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 251 / var(--tw-bg-opacity, 1));
}

.bg-black{
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

.bg-border{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.bg-error-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.bg-gray-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.bg-gray-900{
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}

.bg-primary{
  --tw-bg-opacity: 1;
  background-color: rgb(0 68 102 / var(--tw-bg-opacity, 1));
}

.bg-primary-100{
  --tw-bg-opacity: 1;
  background-color: rgb(224 242 255 / var(--tw-bg-opacity, 1));
}

.bg-primary-200{
  --tw-bg-opacity: 1;
  background-color: rgb(179 217 255 / var(--tw-bg-opacity, 1));
}

.bg-primary-50{
  --tw-bg-opacity: 1;
  background-color: rgb(240 248 255 / var(--tw-bg-opacity, 1));
}

.bg-primary-900{
  --tw-bg-opacity: 1;
  background-color: rgb(0 34 68 / var(--tw-bg-opacity, 1));
}

.bg-secondary{
  --tw-bg-opacity: 1;
  background-color: rgb(0 119 204 / var(--tw-bg-opacity, 1));
}

.bg-secondary-100{
  --tw-bg-opacity: 1;
  background-color: rgb(224 242 255 / var(--tw-bg-opacity, 1));
}

.bg-secondary-50{
  --tw-bg-opacity: 1;
  background-color: rgb(240 248 255 / var(--tw-bg-opacity, 1));
}

.bg-success{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.bg-success-100{
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}

.bg-success-50{
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.bg-success-500{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.bg-surface{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-warning{
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
}

.bg-warning-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 243 199 / var(--tw-bg-opacity, 1));
}

.bg-warning-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}

.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-yellow-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}

.bg-opacity-50{
  --tw-bg-opacity: 0.5;
}

.bg-gradient-to-br{
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.bg-gradient-to-l{
  background-image: linear-gradient(to left, var(--tw-gradient-stops));
}

.bg-gradient-to-t{
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}

.from-primary{
  --tw-gradient-from: #004466 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 68 102 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary-50{
  --tw-gradient-from: #f0f8ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(240 248 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary-900{
  --tw-gradient-from: #002244 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 34 68 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary-900\/80{
  --tw-gradient-from: rgb(0 34 68 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 34 68 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-secondary-50{
  --tw-gradient-from: #f0f8ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(240 248 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-transparent{
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.to-primary-50{
  --tw-gradient-to: #f0f8ff var(--tw-gradient-to-position);
}

.to-primary-800{
  --tw-gradient-to: #003355 var(--tw-gradient-to-position);
}

.to-secondary-50{
  --tw-gradient-to: #f0f8ff var(--tw-gradient-to-position);
}

.to-transparent{
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.fill-current{
  fill: currentColor;
}

.object-contain{
  -o-object-fit: contain;
     object-fit: contain;
}

.object-cover{
  -o-object-fit: cover;
     object-fit: cover;
}

.p-2{
  padding: 0.5rem;
}

.p-3{
  padding: 0.75rem;
}

.p-4{
  padding: 1rem;
}

.p-6{
  padding: 1.5rem;
}

.p-8{
  padding: 2rem;
}

.px-1{
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-8{
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-12{
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-16{
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-8{
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.pl-8{
  padding-left: 2rem;
}

.pr-10{
  padding-right: 2.5rem;
}

.pr-12{
  padding-right: 3rem;
}

.pr-8{
  padding-right: 2rem;
}

.pt-8{
  padding-top: 2rem;
}

.text-left{
  text-align: left;
}

.text-center{
  text-align: center;
}

.text-right{
  text-align: right;
}

.font-cairo{
  font-family: Cairo, sans-serif;
}

.font-inter{
  font-family: Inter, sans-serif;
}

.font-mono{
  font-family: JetBrains Mono, monospace;
}

.text-2xl{
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl{
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl{
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm{
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold{
  font-weight: 700;
}

.font-semibold{
  font-weight: 600;
}

.italic{
  font-style: italic;
}

.leading-relaxed{
  line-height: 1.625;
}

.leading-tight{
  line-height: 1.25;
}

.text-accent{
  --tw-text-opacity: 1;
  color: rgb(255 107 53 / var(--tw-text-opacity, 1));
}

.text-accent-600{
  --tw-text-opacity: 1;
  color: rgb(229 90 43 / var(--tw-text-opacity, 1));
}

.text-accent-700{
  --tw-text-opacity: 1;
  color: rgb(204 74 33 / var(--tw-text-opacity, 1));
}

.text-accent-800{
  --tw-text-opacity: 1;
  color: rgb(179 58 23 / var(--tw-text-opacity, 1));
}

.text-border{
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.text-error-600{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.text-error-700{
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.text-primary{
  --tw-text-opacity: 1;
  color: rgb(0 68 102 / var(--tw-text-opacity, 1));
}

.text-primary-100{
  --tw-text-opacity: 1;
  color: rgb(224 242 255 / var(--tw-text-opacity, 1));
}

.text-primary-200{
  --tw-text-opacity: 1;
  color: rgb(179 217 255 / var(--tw-text-opacity, 1));
}

.text-primary-500{
  --tw-text-opacity: 1;
  color: rgb(26 136 255 / var(--tw-text-opacity, 1));
}

.text-primary-600{
  --tw-text-opacity: 1;
  color: rgb(0 102 204 / var(--tw-text-opacity, 1));
}

.text-primary-700{
  --tw-text-opacity: 1;
  color: rgb(0 68 102 / var(--tw-text-opacity, 1));
}

.text-primary-800{
  --tw-text-opacity: 1;
  color: rgb(0 51 85 / var(--tw-text-opacity, 1));
}

.text-secondary{
  --tw-text-opacity: 1;
  color: rgb(0 119 204 / var(--tw-text-opacity, 1));
}

.text-secondary-600{
  --tw-text-opacity: 1;
  color: rgb(0 102 179 / var(--tw-text-opacity, 1));
}

.text-secondary-700{
  --tw-text-opacity: 1;
  color: rgb(0 85 153 / var(--tw-text-opacity, 1));
}

.text-secondary-800{
  --tw-text-opacity: 1;
  color: rgb(0 68 128 / var(--tw-text-opacity, 1));
}

.text-success{
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.text-success-500{
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.text-success-600{
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.text-success-700{
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}

.text-success-800{
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}

.text-text-primary{
  --tw-text-opacity: 1;
  color: rgb(26 26 26 / var(--tw-text-opacity, 1));
}

.text-text-secondary{
  --tw-text-opacity: 1;
  color: rgb(102 102 102 / var(--tw-text-opacity, 1));
}

.text-warning{
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}

.text-warning-400{
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}

.text-warning-500{
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}

.text-warning-600{
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity, 1));
}

.text-warning-700{
  --tw-text-opacity: 1;
  color: rgb(180 83 9 / var(--tw-text-opacity, 1));
}

.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.line-through{
  text-decoration-line: line-through;
}

.opacity-60{
  opacity: 0.6;
}

.opacity-90{
  opacity: 0.9;
}

.shadow-medium{
  --tw-shadow: 0 8px 24px rgba(0, 68, 102, 0.15);
  --tw-shadow-colored: 0 8px 24px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-soft{
  --tw-shadow: 0 4px 12px rgba(0, 68, 102, 0.1);
  --tw-shadow-colored: 0 4px 12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-strong{
  --tw-shadow: 0 16px 48px rgba(0, 68, 102, 0.2);
  --tw-shadow-colored: 0 16px 48px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-300{
  transition-duration: 300ms;
}

.duration-500{
  transition-duration: 500ms;
}

.shadow-soft {
  box-shadow: var(--shadow-soft);
}

.shadow-medium {
  box-shadow: var(--shadow-medium);
}

.shadow-strong {
  box-shadow: var(--shadow-strong);
}

:root {
  /* Primary Colors */
  --color-primary: #004466;
  /* Deep medical authority, trustworthy foundation */
  --color-primary-50: #f0f8ff;
  /* Very light blue tint */
  --color-primary-100: #e0f2ff;
  /* Light blue tint */
  --color-primary-200: #b3d9ff;
  /* Medium light blue */
  --color-primary-300: #80c4ff;
  /* Medium blue */
  --color-primary-400: #4da6ff;
  /* Medium dark blue */
  --color-primary-500: #1a88ff;
  /* Standard blue */
  --color-primary-600: #0066cc;
  /* Dark blue */
  --color-primary-700: #004466;
  /* Primary - deep medical authority */
  --color-primary-800: #003355;
  /* Darker blue */
  --color-primary-900: #002244;
  /* Darkest blue */
  /* Secondary Colors */
  --color-secondary: #0077cc;
  /* Innovation accessibility, approachable expertise */
  --color-secondary-50: #f0f8ff;
  /* Very light secondary */
  --color-secondary-100: #e0f2ff;
  /* Light secondary */
  --color-secondary-200: #b3d9ff;
  /* Medium light secondary */
  --color-secondary-300: #80c4ff;
  /* Medium secondary */
  --color-secondary-400: #4da6ff;
  /* Medium dark secondary */
  --color-secondary-500: #0077cc;
  /* Secondary - innovation accessibility */
  --color-secondary-600: #0066b3;
  /* Dark secondary */
  --color-secondary-700: #005599;
  /* Darker secondary */
  --color-secondary-800: #004480;
  /* Very dark secondary */
  --color-secondary-900: #003366;
  /* Darkest secondary */
  /* Accent Colors */
  --color-accent: #ff6b35;
  /* Attention highlights, breakthrough moments */
  --color-accent-50: #fff5f2;
  /* Very light accent */
  --color-accent-100: #ffebe5;
  /* Light accent */
  --color-accent-200: #ffd6cc;
  /* Medium light accent */
  --color-accent-300: #ffb399;
  /* Medium accent */
  --color-accent-400: #ff9066;
  /* Medium dark accent */
  --color-accent-500: #ff6b35;
  /* Accent - attention highlights */
  --color-accent-600: #e55a2b;
  /* Dark accent */
  --color-accent-700: #cc4a21;
  /* Darker accent */
  --color-accent-800: #b33a17;
  /* Very dark accent */
  --color-accent-900: #992a0d;
  /* Darkest accent */
  /* Background Colors */
  --color-background: #f8f9fb;
  /* Clean reading canvas, reduces eye strain */
  --color-surface: #ffffff;
  /* Content elevation, clear separation */
  /* Text Colors */
  --color-text-primary: #1a1a1a;
  /* Extended reading comfort, maximum legibility */
  --color-text-secondary: #666666;
  /* Clear hierarchy, supporting information */
  /* Status Colors */
  --color-success: #22c55e;
  /* Learning progress, positive reinforcement - green-500 */
  --color-success-50: #f0fdf4;
  /* Very light success - green-50 */
  --color-success-100: #dcfce7;
  /* Light success - green-100 */
  --color-success-200: #bbf7d0;
  /* Medium light success - green-200 */
  --color-success-300: #86efac;
  /* Medium success - green-300 */
  --color-success-400: #4ade80;
  /* Medium dark success - green-400 */
  --color-success-500: #22c55e;
  /* Success - green-500 */
  --color-success-600: #16a34a;
  /* Dark success - green-600 */
  --color-success-700: #15803d;
  /* Darker success - green-700 */
  --color-success-800: #166534;
  /* Very dark success - green-800 */
  --color-success-900: #14532d;
  /* Darkest success - green-900 */
  --color-warning: #f59e0b;
  /* Important medical notes, careful attention - amber-500 */
  --color-warning-50: #fffbeb;
  /* Very light warning - amber-50 */
  --color-warning-100: #fef3c7;
  /* Light warning - amber-100 */
  --color-warning-200: #fde68a;
  /* Medium light warning - amber-200 */
  --color-warning-300: #fcd34d;
  /* Medium warning - amber-300 */
  --color-warning-400: #fbbf24;
  /* Medium dark warning - amber-400 */
  --color-warning-500: #f59e0b;
  /* Warning - amber-500 */
  --color-warning-600: #d97706;
  /* Dark warning - amber-600 */
  --color-warning-700: #b45309;
  /* Darker warning - amber-700 */
  --color-warning-800: #92400e;
  /* Very dark warning - amber-800 */
  --color-warning-900: #78350f;
  /* Darkest warning - amber-900 */
  --color-error: #ef4444;
  /* Critical corrections, helpful guidance - red-500 */
  --color-error-50: #fef2f2;
  /* Very light error - red-50 */
  --color-error-100: #fee2e2;
  /* Light error - red-100 */
  --color-error-200: #fecaca;
  /* Medium light error - red-200 */
  --color-error-300: #fca5a5;
  /* Medium error - red-300 */
  --color-error-400: #f87171;
  /* Medium dark error - red-400 */
  --color-error-500: #ef4444;
  /* Error - red-500 */
  --color-error-600: #dc2626;
  /* Dark error - red-600 */
  --color-error-700: #b91c1c;
  /* Darker error - red-700 */
  --color-error-800: #991b1b;
  /* Very dark error - red-800 */
  --color-error-900: #7f1d1d;
  /* Darkest error - red-900 */
  /* Border Colors */
  --color-border: #e5e7eb;
  /* Clean separation - gray-200 */
  --color-border-light: #f3f4f6;
  /* Subtle separation - gray-100 */
  --color-border-dark: #d1d5db;
  /* Stronger separation - gray-300 */
  /* Shadow Values */
  --shadow-soft: 0 4px 12px rgba(0, 68, 102, 0.1);
  /* Subtle depth for content cards */
  --shadow-medium: 0 8px 24px rgba(0, 68, 102, 0.15);
  /* Medium depth for modals */
  --shadow-strong: 0 16px 48px rgba(0, 68, 102, 0.2);
  /* Strong depth for overlays */
  /* Animation Timing */
  --transition-smooth: 300ms ease-out;
  /* Smooth transitions for hover states */
  --transition-quick: 200ms ease-out;
  /* Quick transitions for micro-interactions */
}

.hover\:bg-accent-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 214 204 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(0 68 102 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(240 248 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-secondary-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(179 217 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-warning-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(253 230 138 / var(--tw-bg-opacity, 1));
}

.hover\:text-accent-800:hover{
  --tw-text-opacity: 1;
  color: rgb(179 58 23 / var(--tw-text-opacity, 1));
}

.hover\:text-primary:hover{
  --tw-text-opacity: 1;
  color: rgb(0 68 102 / var(--tw-text-opacity, 1));
}

.hover\:text-primary-700:hover{
  --tw-text-opacity: 1;
  color: rgb(0 68 102 / var(--tw-text-opacity, 1));
}

.hover\:text-primary-800:hover{
  --tw-text-opacity: 1;
  color: rgb(0 51 85 / var(--tw-text-opacity, 1));
}

.hover\:text-secondary-700:hover{
  --tw-text-opacity: 1;
  color: rgb(0 85 153 / var(--tw-text-opacity, 1));
}

.hover\:text-success-700:hover{
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}

.hover\:text-warning-700:hover{
  --tw-text-opacity: 1;
  color: rgb(180 83 9 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover{
  text-decoration-line: underline;
}

.hover\:shadow-medium:hover{
  --tw-shadow: 0 8px 24px rgba(0, 68, 102, 0.15);
  --tw-shadow-colored: 0 8px 24px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  box-shadow: var(--shadow-medium);
}

.focus\:ring-success-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}

.group:hover .group-hover\:scale-105{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@media (min-width: 640px){
  .sm\:flex-row{
    flex-direction: row;
  }

  .sm\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px){
  .md\:block{
    display: block;
  }

  .md\:hidden{
    display: none;
  }

  .md\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:text-5xl{
    font-size: 3rem;
    line-height: 1;
  }
}

@media (min-width: 1024px){
  .lg\:order-1{
    order: 1;
  }

  .lg\:order-2{
    order: 2;
  }

  .lg\:col-span-2{
    grid-column: span 2 / span 2;
  }

  .lg\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:flex-row{
    flex-direction: row;
  }

  .lg\:justify-start{
    justify-content: flex-start;
  }

  .lg\:justify-end{
    justify-content: flex-end;
  }

  .lg\:px-8{
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:text-right{
    text-align: right;
  }
}