<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>آفاق بحثية في التصوير الجزيئي وتطبيقات الذكاء الاصطناعي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            animation: fadeInUp 1s ease-out;
        }

        .book-title {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .author-info {
            font-size: 1.2em;
            color: #7f8c8d;
            margin-bottom: 10px;
        }

        .contact-info {
            font-size: 0.9em;
            color: #95a5a6;
            margin-bottom: 20px;
        }

        .description {
            font-size: 1.1em;
            color: #34495e;
            background: rgba(52, 73, 94, 0.05);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            line-height: 1.8;
        }

        .nav-tabs {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .tab-button {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            color: #2c3e50;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .tab-button:hover {
            background: #3498db;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }

        .tab-button.active {
            background: #2980b9;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(41, 128, 185, 0.4);
        }

        .content-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 40px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: none;
            animation: fadeInUp 0.6s ease-out;
        }

        .content-section.active {
            display: block;
        }

        .section-title {
            font-size: 2em;
            color: #2c3e50;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 3px solid #3498db;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 50px;
            height: 3px;
            background: #e74c3c;
        }

        .part {
            margin-bottom: 40px;
            padding: 25px;
            background: rgba(52, 152, 219, 0.05);
            border-radius: 15px;
            border-left: 5px solid #3498db;
        }

        .part-title {
            font-size: 1.5em;
            color: #2980b9;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .chapter {
            margin-bottom: 25px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            border-left: 3px solid #e74c3c;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .chapter:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .chapter-title {
            font-size: 1.2em;
            color: #e74c3c;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .chapter-content {
            color: #5a6c7d;
            line-height: 1.7;
        }

        .chapter-content ul {
            padding-right: 20px;
            margin-top: 10px;
        }

        .chapter-content li {
            margin-bottom: 8px;
            color: #7f8c8d;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.9);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-top: 4px solid #3498db;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
        }

        .feature-title {
            font-size: 1.3em;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .feature-description {
            color: #7f8c8d;
            line-height: 1.6;
        }

        .requirements-section {
            background: rgba(46, 204, 113, 0.1);
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            border-left: 5px solid #2ecc71;
        }

        .requirements-title {
            font-size: 1.4em;
            color: #27ae60;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .requirements-content {
            color: #2c3e50;
            line-height: 1.7;
        }

        .target-audience {
            background: rgba(155, 89, 182, 0.1);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            border-left: 5px solid #9b59b6;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-element {
            position: absolute;
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(100vh) rotate(0deg); }
            100% { transform: translateY(-100px) rotate(360deg); }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #e74c3c);
            z-index: 1000;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .book-title {
                font-size: 1.8em;
            }
            
            .content-section {
                padding: 20px;
            }
            
            .nav-tabs {
                gap: 5px;
            }
            
            .tab-button {
                padding: 10px 15px;
                font-size: 0.9em;
            }
        }
    </style>
</head>
<body>
    <div class="progress-bar" id="progressBar"></div>
    
    <div class="floating-elements" id="floatingElements"></div>

    <div class="container">
        <div class="header">
            <h1 class="book-title">آفاق بحثية في التصوير الجزيئي وتطبيقات الذكاء الاصطناعي</h1>
            <div class="author-info">
                <strong>المؤلف:</strong> د. محمد يعقوب إسماعيل يعقوب
            </div>
            <div class="author-info">
                نائب عميد كلية الهندسة - أستاذ مساعد الهندسة الطبية الحيوية
            </div>
            <div class="author-info">
                جامعة السودان للعلوم والتكنولوجيا
            </div>
            <div class="contact-info">
                البريد الإلكتروني: <EMAIL> | الهاتف: +966538076790
            </div>
            <div class="description">
                كتاب رائد ومستقبلي، يستعرض أحدث التوجهات البحثية التي تشكل ملامح الجيل القادم من التصوير الطبي النووي. لا يقتصر هذا المرجع على التقنيات الحالية، بل يغوص في أعماق الأبحاث المتطورة في مجال التصوير الجزيئي، مثل أنظمة التصوير لكامل الجسم والمستحضرات الصيدلانية الإشعاعية المبتكرة. والأهم من ذلك، يستكشف الكتاب الدور المحوري الذي يلعبه الذكاء الاصطناعي كقوة دافعة لهذه الثورة.
            </div>
        </div>

        <div class="nav-tabs">
            <button class="tab-button active" data-tab="overview">نظرة عامة</button>
            <button class="tab-button" data-tab="structure">هيكل الكتاب</button>
            <button class="tab-button" data-tab="requirements">المتطلبات التقنية</button>
            <button class="tab-button" data-tab="audience">الجمهور المستهدف</button>
        </div>

        <div class="content-section active" id="overview">
            <h2 class="section-title">نظرة عامة على الكتاب</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3 class="feature-title">🔬 التصوير الجزيئي المتقدم</h3>
                    <p class="feature-description">
                        استكشاف أحدث تقنيات التصوير الجزيئي بما في ذلك أنظمة Total-Body PET و PET/MRI المتطورة
                    </p>
                </div>
                
                <div class="feature-card">
                    <h3 class="feature-title">🤖 الذكاء الاصطناعي في الطب</h3>
                    <p class="feature-description">
                        تطبيقات الذكاء الاصطناعي والتعلم العميق في تحليل الصور الطبية وتحسين دقة التشخيص
                    </p>
                </div>
                
                <div class="feature-card">
                    <h3 class="feature-title">📊 علم الأشعة الكمي</h3>
                    <p class="feature-description">
                        مفاهيم Radiomics واستخراج البيانات الخفية من الصور الطبية للتنبؤ بالنتائج السريرية
                    </p>
                </div>
                
                <div class="feature-card">
                    <h3 class="feature-title">🎯 الطب الشخصي</h3>
                    <p class="feature-description">
                        النماذج التنبؤية لتخصيص العلاج وتحسين نتائج المرضى في عصر الطب الدقيق
                    </p>
                </div>
            </div>
        </div>

        <div class="content-section" id="structure">
            <h2 class="section-title">هيكل الكتاب المقترح</h2>
            
            <div class="part">
                <h3 class="part-title">الجزء الأول: الجبهات الجديدة في تقنيات التصوير الجزيئي</h3>
                
                <div class="chapter">
                    <h4 class="chapter-title">الفصل الأول: ما وراء PET/CT التقليدي: أنظمة التصوير لكامل الجسم</h4>
                    <div class="chapter-content">
                        <ul>
                            <li>فيزياء وميزات أنظمة Total-Body PET</li>
                            <li>القدرة على تتبع الديناميكيات الدوائية في كامل الجسم وبشكل فوري</li>
                            <li>إمكانات خفض الجرعة الإشعاعية بشكل جذري أو تقليل زمن التصوير</li>
                        </ul>
                    </div>
                </div>
                
                <div class="chapter">
                    <h4 class="chapter-title">الفصل الثاني: صعود نجم PET/MRI</h4>
                    <div class="chapter-content">
                        <ul>
                            <li>الفيزياء التآزرية: الجمع بين الحساسية الفائقة لـ PET والتباين الاستثنائي للأنسجة الرخوة في MRI</li>
                            <li>التحديات التقنية والحلول (مثل تصحيح التوهين في بيئة MRI)</li>
                            <li>التطبيقات البحثية الواعدة في علم الأعصاب والأورام</li>
                        </ul>
                    </div>
                </div>
                
                <div class="chapter">
                    <h4 class="chapter-title">الفصل الثالث: مستحضرات صيدلانية إشعاعية مبتكرة</h4>
                    <div class="chapter-content">
                        <ul>
                            <li>تطوير متتبعات (Tracers) تستهدف عمليات بيولوجية جديدة</li>
                            <li>المتتبعات المنشطة بالضوء (Photoactivatable Probes)</li>
                            <li>دور النانوتكنولوجي في توصيل الأدوية المشعة</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="part">
                <h3 class="part-title">الجزء الثاني: ثورة الذكاء الاصطناعي في تحليل الصور الطبية</h3>
                
                <div class="chapter">
                    <h4 class="chapter-title">الفصل الرابع: مفاهيم الذكاء الاصطناعي والتعلم العميق للفيزيائي الطبي</h4>
                    <div class="chapter-content">
                        <ul>
                            <li>مقدمة مبسطة للشبكات العصبونية الاصطناعية والشبكات العصبونية الالتفافية</li>
                            <li>الفرق بين التعلم الخاضع للإشراف، وغير الخاضع للإشراف، والتعلم المعزز</li>
                            <li>بنية وهيكلية نماذج التعلم العميق المستخدمة في التصوير</li>
                        </ul>
                    </div>
                </div>
                
                <div class="chapter">
                    <h4 class="chapter-title">الفصل الخامس: علم الأشعة (Radiomics): استخراج البيانات الخفية من الصور</h4>
                    <div class="chapter-content">
                        <ul>
                            <li>مفهوم استخراج مئات السمات الكمية من الصور الطبية</li>
                            <li>أنواع السمات: الشكل، الكثافة، والملمس</li>
                            <li>ربط سمات Radiomics بالخصائص الجينية للورم</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="part">
                <h3 class="part-title">الجزء الثالث: تطبيقات الذكاء الاصطناعي: من الصورة إلى القرار السريري</h3>
                
                <div class="chapter">
                    <h4 class="chapter-title">الفصل السادس: إعادة بناء الصور وتحسينها بواسطة الذكاء الاصطناعي</h4>
                    <div class="chapter-content">
                        <ul>
                            <li>استخدام نماذج التعلم العميق لإعادة بناء الصور من بيانات أولية أقل</li>
                            <li>تقنيات إزالة الضوضاء وتحسين دقة الصورة</li>
                            <li>توليد صور افتراضية لتصحيح التوهين في PET/MRI</li>
                        </ul>
                    </div>
                </div>
                
                <div class="chapter">
                    <h4 class="chapter-title">الفصل السابع: التشخيص والتجزئة الآلية</h4>
                    <div class="chapter-content">
                        <ul>
                            <li>استخدام الذكاء الاصطناعي للكشف عن الآفات وتحديدها</li>
                            <li>تطبيقات في حساب الحجم الأيضي للورم والحمل الورمي الكلي</li>
                        </ul>
                    </div>
                </div>
                
                <div class="chapter">
                    <h4 class="chapter-title">الفصل الثامن: النماذج التنبؤية وتخصيص العلاج</h4>
                    <div class="chapter-content">
                        <ul>
                            <li>التنبؤ بالاستجابة للعلاج الكيميائي أو الإشعاعي</li>
                            <li>التنبؤ بفرص نجاة المريض وتطور المرض</li>
                            <li>تكييف خطط العلاج الإشعاعي بناءً على التغيرات في صور المتابعة</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="part">
                <h3 class="part-title">الجزء الرابع: التحديات والآفاق المستقبلية</h3>
                
                <div class="chapter">
                    <h4 class="chapter-title">الفصل التاسع: التحديات الأخلاقية والتنظيمية</h4>
                    <div class="chapter-content">
                        <ul>
                            <li>مشكلة "الصندوق الأسود" وقابلية تفسير قرارات الذكاء الاصطناعي</li>
                            <li>التحيز في الخوارزميات وأهمية البيانات المتنوعة</li>
                            <li>متطلبات التحقق والمصادقة للحصول على الموافقات التنظيمية</li>
                        </ul>
                    </div>
                </div>
                
                <div class="chapter">
                    <h4 class="chapter-title">الفصل العاشر: مستقبل الممارسة الطبية في عصر الذكاء الاصطناعي</h4>
                    <div class="chapter-content">
                        <ul>
                            <li>تطور دور الفيزيائي الطبي، طبيب الأشعة، وطبيب الأورام</li>
                            <li>نحو تكامل "الطبيب المعزز بالذكاء الاصطناعي"</li>
                            <li>نظرة على التوائم الرقمية في الطب الشخصي</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="content-section" id="requirements">
            <h2 class="section-title">متطلبات الرسومات والأشكال والجداول</h2>
            
            <div class="requirements-section">
                <h3 class="requirements-title">📊 الرسومات والأشكال</h3>
                <div class="requirements-content">
                    <ul>
                        <li><strong>مخططات انسيابية:</strong> توضح مسار العمل الكامل لتطبيق Radiomics، من الحصول على الصورة إلى بناء النموذج التنبؤي</li>
                        <li><strong>مقارنات بصرية:</strong> صورة PET منخفضة الجرعة قبل وبعد تحسينها بالذكاء الاصطناعي</li>
                        <li><strong>رسومات توضيحية:</strong> لهياكل الشبكات العصبونية (مثل U-Net) تظهر طبقاتها المختلفة</li>
                        <li><strong>خرائط حرارية:</strong> توضح المناطق التي يركز عليها نموذج الذكاء الاصطناعي لاتخاذ قرار تشخيصي</li>
                        <li><strong>منحنيات ROC:</strong> لتقييم أداء النماذج التنبؤية</li>
                    </ul>
                </div>
            </div>

            <div class="requirements-section">
                <h3 class="requirements-title">📋 الجداول</h3>
                <div class="requirements-content">
                    <ul>
                        <li><strong>جدول مقارن:</strong> بين تقنيات التصوير التقليدية والتقنيات البحثية الجديدة</li>
                        <li><strong>جدول نماذج التعلم العميق:</strong> يلخص أشهر النماذج وتطبيقاتها في التصوير الطبي</li>
                        <li><strong>جدول قواعد البيانات:</strong> يسرد قواعد البيانات العامة والمتاحة للباحثين لتدريب نماذج الذكاء الاصطناعي</li>
                    </ul>
                </div>
            </div>

            <div class="requirements-section">
                <h3 class="requirements-title">🔢 المعادلات</h3>
                <div class="requirements-content">
                    <ul>
                        <li><strong>التركيز على المفاهيم:</strong> بدلاً من الاشتقاقات المعقدة</li>
                        <li><strong>دالة الخسارة:</strong> المستخدمة في تدريب الشبكات العصبونية مع شرح مبسط</li>
                        <li><strong>معادلات تقييم الأداء:</strong> مثل الدقة، الحساسية، والنوعية</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="content-section" id="audience">
            <h2 class="section-title">الجمهور المستهدف</h2>
            
            <div class="target-audience">
                <h3 class="feature-title">👥 الفئات المستهدفة</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4 class="feature-title">🔬 الباحثون</h4>
                        <p class="feature-description">
                            الباحثون في الفيزياء الطبية والتصوير الطبي الساعون لفهم أحدث التطورات في مجالهم
                        </p>
                    </div>
                    
                    <div class="feature-card">
                        <h4 class="feature-title">💻 علماء البيانات</h4>
                        <p class="feature-description">
                            المتخصصون في علم البيانات في مجال الرعاية الصحية والمهتمون بتطبيقات الذكاء الاصطناعي الطبية
                        </p>
                    </div>
                    
                    <div class="feature-card">
                        <h4 class="feature-title">🎓 طلاب الدراسات العليا</h4>
                        <p class="feature-description">
                            طلاب الدكتوراه وما بعد الدكتوراه في التخصصات ذات الصلة بالتصوير الطبي والذكاء الاصطناعي
                        </p>
                    </div>
                    
                    <div class="feature-card">
                        <h4 class="feature-title">⚕️ الأطباء</h4>
                        <p class="feature-description">
                            أطباء الأشعة والطب النووي ذوو التوجه البحثي والمهتمون بالتطورات التقنية
                        </p>
                    </div>
                    
                    <div class="feature-card">
                        <h4 class="feature-title">🚀 قادة الفكر</h4>
                        <p class="feature-description">
                            المسؤولون عن تطوير التقنيات الطبية والمهتمون بالاتجاهات المستقبلية في الطب
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab switching functionality
        const tabButtons = document.querySelectorAll('.tab-button');
        const contentSections = document.querySelectorAll('.content-section');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons and sections
                tabButtons.forEach(btn => btn.classList.remove('active'));
                contentSections.forEach(section => section.classList.remove('active'));
                
                // Add active class to clicked button
                button.classList.add('active');
                
                // Show corresponding content section
                const targetTab = button.getAttribute('data-tab');
                document.getElementById(targetTab).classList.add('active');
            });
        });

        // Progress bar functionality
        function updateProgressBar() {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.scrollHeight - window.inner