@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Primary Colors */
  --color-primary: #004466; /* Deep medical authority, trustworthy foundation */
  --color-primary-50: #f0f8ff; /* Very light blue tint */
  --color-primary-100: #e0f2ff; /* Light blue tint */
  --color-primary-200: #b3d9ff; /* Medium light blue */
  --color-primary-300: #80c4ff; /* Medium blue */
  --color-primary-400: #4da6ff; /* Medium dark blue */
  --color-primary-500: #1a88ff; /* Standard blue */
  --color-primary-600: #0066cc; /* Dark blue */
  --color-primary-700: #004466; /* Primary - deep medical authority */
  --color-primary-800: #003355; /* Darker blue */
  --color-primary-900: #002244; /* Darkest blue */

  /* Secondary Colors */
  --color-secondary: #0077cc; /* Innovation accessibility, approachable expertise */
  --color-secondary-50: #f0f8ff; /* Very light secondary */
  --color-secondary-100: #e0f2ff; /* Light secondary */
  --color-secondary-200: #b3d9ff; /* Medium light secondary */
  --color-secondary-300: #80c4ff; /* Medium secondary */
  --color-secondary-400: #4da6ff; /* Medium dark secondary */
  --color-secondary-500: #0077cc; /* Secondary - innovation accessibility */
  --color-secondary-600: #0066b3; /* Dark secondary */
  --color-secondary-700: #005599; /* Darker secondary */
  --color-secondary-800: #004480; /* Very dark secondary */
  --color-secondary-900: #003366; /* Darkest secondary */

  /* Accent Colors */
  --color-accent: #ff6b35; /* Attention highlights, breakthrough moments */
  --color-accent-50: #fff5f2; /* Very light accent */
  --color-accent-100: #ffebe5; /* Light accent */
  --color-accent-200: #ffd6cc; /* Medium light accent */
  --color-accent-300: #ffb399; /* Medium accent */
  --color-accent-400: #ff9066; /* Medium dark accent */
  --color-accent-500: #ff6b35; /* Accent - attention highlights */
  --color-accent-600: #e55a2b; /* Dark accent */
  --color-accent-700: #cc4a21; /* Darker accent */
  --color-accent-800: #b33a17; /* Very dark accent */
  --color-accent-900: #992a0d; /* Darkest accent */

  /* Background Colors */
  --color-background: #f8f9fb; /* Clean reading canvas, reduces eye strain */
  --color-surface: #ffffff; /* Content elevation, clear separation */

  /* Text Colors */
  --color-text-primary: #1a1a1a; /* Extended reading comfort, maximum legibility */
  --color-text-secondary: #666666; /* Clear hierarchy, supporting information */

  /* Status Colors */
  --color-success: #22c55e; /* Learning progress, positive reinforcement - green-500 */
  --color-success-50: #f0fdf4; /* Very light success - green-50 */
  --color-success-100: #dcfce7; /* Light success - green-100 */
  --color-success-200: #bbf7d0; /* Medium light success - green-200 */
  --color-success-300: #86efac; /* Medium success - green-300 */
  --color-success-400: #4ade80; /* Medium dark success - green-400 */
  --color-success-500: #22c55e; /* Success - green-500 */
  --color-success-600: #16a34a; /* Dark success - green-600 */
  --color-success-700: #15803d; /* Darker success - green-700 */
  --color-success-800: #166534; /* Very dark success - green-800 */
  --color-success-900: #14532d; /* Darkest success - green-900 */

  --color-warning: #f59e0b; /* Important medical notes, careful attention - amber-500 */
  --color-warning-50: #fffbeb; /* Very light warning - amber-50 */
  --color-warning-100: #fef3c7; /* Light warning - amber-100 */
  --color-warning-200: #fde68a; /* Medium light warning - amber-200 */
  --color-warning-300: #fcd34d; /* Medium warning - amber-300 */
  --color-warning-400: #fbbf24; /* Medium dark warning - amber-400 */
  --color-warning-500: #f59e0b; /* Warning - amber-500 */
  --color-warning-600: #d97706; /* Dark warning - amber-600 */
  --color-warning-700: #b45309; /* Darker warning - amber-700 */
  --color-warning-800: #92400e; /* Very dark warning - amber-800 */
  --color-warning-900: #78350f; /* Darkest warning - amber-900 */

  --color-error: #ef4444; /* Critical corrections, helpful guidance - red-500 */
  --color-error-50: #fef2f2; /* Very light error - red-50 */
  --color-error-100: #fee2e2; /* Light error - red-100 */
  --color-error-200: #fecaca; /* Medium light error - red-200 */
  --color-error-300: #fca5a5; /* Medium error - red-300 */
  --color-error-400: #f87171; /* Medium dark error - red-400 */
  --color-error-500: #ef4444; /* Error - red-500 */
  --color-error-600: #dc2626; /* Dark error - red-600 */
  --color-error-700: #b91c1c; /* Darker error - red-700 */
  --color-error-800: #991b1b; /* Very dark error - red-800 */
  --color-error-900: #7f1d1d; /* Darkest error - red-900 */

  /* Border Colors */
  --color-border: #e5e7eb; /* Clean separation - gray-200 */
  --color-border-light: #f3f4f6; /* Subtle separation - gray-100 */
  --color-border-dark: #d1d5db; /* Stronger separation - gray-300 */

  /* Shadow Values */
  --shadow-soft: 0 4px 12px rgba(0, 68, 102, 0.1); /* Subtle depth for content cards */
  --shadow-medium: 0 8px 24px rgba(0, 68, 102, 0.15); /* Medium depth for modals */
  --shadow-strong: 0 16px 48px rgba(0, 68, 102, 0.2); /* Strong depth for overlays */

  /* Animation Timing */
  --transition-smooth: 300ms ease-out; /* Smooth transitions for hover states */
  --transition-quick: 200ms ease-out; /* Quick transitions for micro-interactions */
}

@layer base {
  body {
    font-family: 'Inter', sans-serif;
    color: var(--color-text-primary);
    background-color: var(--color-background);
    line-height: 1.6;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  .font-mono {
    font-family: 'JetBrains Mono', monospace;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary text-white px-6 py-3 rounded-lg font-cairo font-semibold transition-all duration-300 hover:bg-primary-800 focus:ring-2 focus:ring-primary-300 focus:outline-none;
  }

  .btn-secondary {
    @apply bg-secondary text-white px-6 py-3 rounded-lg font-cairo font-semibold transition-all duration-300 hover:bg-secondary-700 focus:ring-2 focus:ring-secondary-300 focus:outline-none;
  }

  .btn-accent {
    @apply bg-accent text-white px-6 py-3 rounded-lg font-cairo font-semibold transition-all duration-300 hover:bg-accent-600 focus:ring-2 focus:ring-accent-300 focus:outline-none;
  }

  .card {
    @apply bg-surface rounded-lg p-6 shadow-soft border border-border transition-all duration-300 hover:shadow-medium;
  }

  .input-field {
    @apply w-full px-4 py-3 border border-border rounded-lg focus:border-primary focus:ring-2 focus:ring-primary-100 focus:outline-none transition-all duration-200 bg-surface;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent;
  }
}

@layer utilities {
  .shadow-soft {
    box-shadow: var(--shadow-soft);
  }

  .shadow-medium {
    box-shadow: var(--shadow-medium);
  }

  .shadow-strong {
    box-shadow: var(--shadow-strong);
  }

  .transition-smooth {
    transition: var(--transition-smooth);
  }

  .transition-quick {
    transition: var(--transition-quick);
  }
}