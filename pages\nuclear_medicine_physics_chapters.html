<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nuclear Medicine Physics - Detailed Chapters | Dr. <PERSON></title>
    <meta name="description" content="Comprehensive chapter breakdown of Nuclear Medicine Physics with interactive content, figures, tables, and academic citations">
    <meta name="keywords" content="Nuclear Medicine Physics, Medical Physics, Biomedical Engineering, Academic Chapters, Interactive Learning">
    <meta name="author" content="Dr. <PERSON>, SUST-BME">
    
    <!-- Fonts and Icons -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <!-- MathJax for equations -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #7c3aed;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --light-color: #f8fafc;
            --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
            --border-radius: 0.75rem;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --font-primary: 'Inter', sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-primary);
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        /* Header */
        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 4rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s linear infinite;
        }

        @keyframes float {
            0% { transform: translateY(0); }
            100% { transform: translateY(-100px); }
        }

        .page-title {
            font-size: clamp(2rem, 5vw, 3.5rem);
            font-weight: 800;
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
        }

        .page-subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            position: relative;
            z-index: 2;
        }

        .breadcrumb {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            opacity: 0.8;
            position: relative;
            z-index: 2;
        }

        .breadcrumb a {
            color: white;
            text-decoration: none;
            transition: var(--transition);
        }

        .breadcrumb a:hover {
            opacity: 0.7;
        }

        /* Main Content */
        .main-content {
            padding: 4rem 0;
        }

        .chapters-overview {
            background: white;
            border-radius: var(--border-radius);
            padding: 3rem;
            margin-bottom: 3rem;
            box-shadow: var(--shadow-medium);
        }

        .overview-title {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .overview-description {
            font-size: 1.125rem;
            color: var(--text-secondary);
            text-align: center;
            max-width: 800px;
            margin: 0 auto 2rem;
        }

        .overview-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .stat-card {
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, var(--light-color), #e2e8f0);
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-color);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary-color);
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Chapter Cards */
        .chapters-grid {
            display: grid;
            gap: 2rem;
        }

        .chapter-card {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-medium);
            transition: var(--transition);
            border-left: 4px solid var(--primary-color);
        }

        .chapter-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px rgba(0, 0, 0, 0.15);
        }

        .chapter-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            position: relative;
        }

        .chapter-number {
            font-size: 3rem;
            font-weight: 800;
            opacity: 0.3;
            position: absolute;
            top: 1rem;
            right: 2rem;
        }

        .chapter-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 2;
        }

        .chapter-subtitle {
            opacity: 0.9;
            font-size: 1rem;
            position: relative;
            z-index: 2;
        }

        .chapter-content {
            padding: 2rem;
        }

        .chapter-description {
            color: var(--text-secondary);
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .chapter-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .feature-section {
            background: var(--light-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border-left: 3px solid var(--accent-color);
        }

        .feature-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .feature-list {
            list-style: none;
        }

        .feature-list li {
            padding: 0.25rem 0;
            color: var(--text-secondary);
            font-size: 0.9rem;
            position: relative;
            padding-left: 1.5rem;
        }

        .feature-list li::before {
            content: '→';
            position: absolute;
            left: 0;
            color: var(--accent-color);
            font-weight: bold;
        }

        .chapter-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 2rem;
            background: var(--light-color);
            border-top: 1px solid var(--border-color);
        }

        .stats-group {
            display: flex;
            gap: 2rem;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .stat-icon {
            color: var(--primary-color);
        }

        .chapter-actions {
            display: flex;
            gap: 1rem;
        }

        .action-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 0.5rem;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: white;
            color: var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--light-color);
        }

        /* Equations Display */
        .equation-preview {
            background: white;
            padding: 1rem;
            border-radius: 0.5rem;
            border-left: 3px solid var(--success-color);
            margin: 1rem 0;
            font-family: 'Times New Roman', serif;
            text-align: center;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .page-header {
                padding: 2rem 0;
            }

            .chapters-overview {
                padding: 2rem;
            }

            .chapter-content {
                padding: 1.5rem;
            }

            .chapter-features {
                grid-template-columns: 1fr;
            }

            .chapter-stats {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }

            .stats-group {
                flex-wrap: wrap;
            }

            .chapter-actions {
                width: 100%;
                justify-content: stretch;
            }

            .action-btn {
                flex: 1;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Page Header -->
    <header class="page-header">
        <div class="container">
            <nav class="breadcrumb">
                <a href="../index_new.html">Home</a>
                <span>/</span>
                <a href="../nuclear_medicine_book.html">Nuclear Medicine</a>
                <span>/</span>
                <span>Chapter Details</span>
            </nav>
            <h1 class="page-title">Nuclear Medicine Physics</h1>
            <p class="page-subtitle">Comprehensive Chapter Breakdown with Interactive Content</p>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Overview Section -->
            <section class="chapters-overview">
                <h2 class="overview-title">Course Overview</h2>
                <p class="overview-description">
                    This comprehensive course covers the fundamental principles of nuclear medicine physics, 
                    from atomic structure to advanced imaging techniques. Each chapter includes interactive 
                    simulations, detailed figures, comprehensive tables, and extensive academic citations.
                </p>
                
                <div class="overview-stats">
                    <div class="stat-card">
                        <span class="stat-number">12</span>
                        <span class="stat-label">Chapters</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">350+</span>
                        <span class="stat-label">Figures</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">200+</span>
                        <span class="stat-label">Tables</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">500+</span>
                        <span class="stat-label">Citations</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">300+</span>
                        <span class="stat-label">Equations</span>
                    </div>
                </div>
            </section>

            <!-- Chapters Grid -->
            <section class="chapters-grid">
                <!-- Chapter 1 -->
                <article class="chapter-card animate__animated animate__fadeInUp">
                    <div class="chapter-header">
                        <div class="chapter-number">01</div>
                        <h3 class="chapter-title">Atomic Structure and Nuclear Properties</h3>
                        <p class="chapter-subtitle">Foundation of Nuclear Medicine Physics</p>
                    </div>
                    
                    <div class="chapter-content">
                        <p class="chapter-description">
                            This foundational chapter explores the quantum mechanical nature of atoms, nuclear composition, 
                            and the fundamental properties that govern nuclear medicine applications. Students will master 
                            the mathematical frameworks essential for understanding radioactive decay and nuclear interactions.
                        </p>

                        <div class="equation-preview">
                            <strong>Key Equation:</strong> E = mc² (Mass-Energy Equivalence)
                        </div>

                        <div class="chapter-features">
                            <div class="feature-section">
                                <h4 class="feature-title">
                                    <i class="fas fa-chart-line"></i>
                                    Figures & Visualizations
                                </h4>
                                <ul class="feature-list">
                                    <li>Atomic structure diagrams (Bohr vs. Quantum models)</li>
                                    <li>Nuclear binding energy curves</li>
                                    <li>Chart of nuclides with stability regions</li>
                                    <li>3D electron orbital visualizations</li>
                                    <li>Mass spectrometry data representations</li>
                                </ul>
                            </div>

                            <div class="feature-section">
                                <h4 class="feature-title">
                                    <i class="fas fa-table"></i>
                                    Tables & Data
                                </h4>
                                <ul class="feature-list">
                                    <li>Fundamental physical constants</li>
                                    <li>Nuclear properties of medical isotopes</li>
                                    <li>Binding energies per nucleon</li>
                                    <li>Electron configuration tables</li>
                                    <li>Nuclear decay modes comparison</li>
                                </ul>
                            </div>

                            <div class="feature-section">
                                <h4 class="feature-title">
                                    <i class="fas fa-quote-right"></i>
                                    Key Citations
                                </h4>
                                <ul class="feature-list">
                                    <li>Einstein, A. (1905). "Mass-energy equivalence"</li>
                                    <li>Schrödinger, E. (1926). "Wave mechanics"</li>
                                    <li>Weizsäcker, C.F. (1935). "Semi-empirical mass formula"</li>
                                    <li>Segre, E. (1977). "Nuclei and Particles"</li>
                                    <li>Evans, R.D. (1955). "The Atomic Nucleus"</li>
                                </ul>
                            </div>

                            <div class="feature-section">
                                <h4 class="feature-title">
                                    <i class="fas fa-calculator"></i>
                                    Interactive Tools
                                </h4>
                                <ul class="feature-list">
                                    <li>3D atomic structure visualizer</li>
                                    <li>Binding energy calculator</li>
                                    <li>Nuclear stability predictor</li>
                                    <li>Electron configuration builder</li>
                                    <li>Mass-energy conversion tool</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="chapter-stats">
                        <div class="stats-group">
                            <div class="stat-item">
                                <i class="fas fa-image stat-icon"></i>
                                <span>15 Figures</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-table stat-icon"></i>
                                <span>8 Tables</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-calculator stat-icon"></i>
                                <span>25 Equations</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-quote-right stat-icon"></i>
                                <span>45 Citations</span>
                            </div>
                        </div>
                        
                        <div class="chapter-actions">
                            <a href="../nuclear_medicine_book.html#chapter1" class="action-btn btn-primary">
                                <i class="fas fa-play"></i>
                                Start Chapter
                            </a>
                            <a href="#" class="action-btn btn-secondary">
                                <i class="fas fa-download"></i>
                                Download PDF
                            </a>
                        </div>
                    </div>
                </article>

                <!-- Chapter 2 -->
                <article class="chapter-card animate__animated animate__fadeInUp">
                    <div class="chapter-header">
                        <div class="chapter-number">02</div>
                        <h3 class="chapter-title">Radioactive Decay and Nuclear Transformations</h3>
                        <p class="chapter-subtitle">Kinetics and Mathematical Modeling</p>
                    </div>

                    <div class="chapter-content">
                        <p class="chapter-description">
                            Comprehensive study of radioactive decay processes, decay laws, and nuclear transformation mechanisms.
                            This chapter provides the mathematical foundation for understanding radiopharmaceutical kinetics and
                            dosimetry calculations essential in nuclear medicine practice.
                        </p>

                        <div class="equation-preview">
                            <strong>Fundamental Decay Law:</strong> N(t) = N₀e^(-λt)
                        </div>

                        <div class="chapter-features">
                            <div class="feature-section">
                                <h4 class="feature-title">
                                    <i class="fas fa-chart-line"></i>
                                    Figures & Visualizations
                                </h4>
                                <ul class="feature-list">
                                    <li>Decay mode diagrams (α, β⁻, β⁺, EC, γ)</li>
                                    <li>Exponential decay curves with half-life markers</li>
                                    <li>Decay chain schematics (U-238, Th-232 series)</li>
                                    <li>Branching ratio illustrations</li>
                                    <li>Secular equilibrium graphs</li>
                                    <li>Activity vs. time interactive plots</li>
                                </ul>
                            </div>

                            <div class="feature-section">
                                <h4 class="feature-title">
                                    <i class="fas fa-table"></i>
                                    Tables & Data
                                </h4>
                                <ul class="feature-list">
                                    <li>Medical isotope half-lives and decay constants</li>
                                    <li>Decay mode probabilities and Q-values</li>
                                    <li>Bateman equation coefficients</li>
                                    <li>Generator system parameters</li>
                                    <li>Specific activity calculations</li>
                                </ul>
                            </div>

                            <div class="feature-section">
                                <h4 class="feature-title">
                                    <i class="fas fa-quote-right"></i>
                                    Key Citations
                                </h4>
                                <ul class="feature-list">
                                    <li>Rutherford, E. & Soddy, F. (1903). "Radioactive change"</li>
                                    <li>Bateman, H. (1910). "Solution of differential equations"</li>
                                    <li>Geiger, H. & Nuttal, J.M. (1911). "Geiger-Nuttal rule"</li>
                                    <li>Fermi, E. (1934). "Theory of beta decay"</li>
                                    <li>Segre, E. & Wiegand, C. (1949). "Electron capture"</li>
                                </ul>
                            </div>

                            <div class="feature-section">
                                <h4 class="feature-title">
                                    <i class="fas fa-calculator"></i>
                                    Interactive Tools
                                </h4>
                                <ul class="feature-list">
                                    <li>Real-time decay simulator</li>
                                    <li>Half-life calculator</li>
                                    <li>Activity conversion tools</li>
                                    <li>Decay chain analyzer</li>
                                    <li>Generator equilibrium calculator</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="chapter-stats">
                        <div class="stats-group">
                            <div class="stat-item">
                                <i class="fas fa-image stat-icon"></i>
                                <span>22 Figures</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-table stat-icon"></i>
                                <span>12 Tables</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-calculator stat-icon"></i>
                                <span>35 Equations</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-quote-right stat-icon"></i>
                                <span>52 Citations</span>
                            </div>
                        </div>

                        <div class="chapter-actions">
                            <a href="../nuclear_medicine_book.html#chapter2" class="action-btn btn-primary">
                                <i class="fas fa-play"></i>
                                Start Chapter
                            </a>
                            <a href="#" class="action-btn btn-secondary">
                                <i class="fas fa-download"></i>
                                Download PDF
                            </a>
                        </div>
                    </div>
                </article>

                <!-- Chapter 3 -->
                <article class="chapter-card animate__animated animate__fadeInUp">
                    <div class="chapter-header">
                        <div class="chapter-number">03</div>
                        <h3 class="chapter-title">Radiation Interaction with Matter</h3>
                        <p class="chapter-subtitle">Photon and Particle Physics</p>
                    </div>

                    <div class="chapter-content">
                        <p class="chapter-description">
                            Detailed analysis of radiation-matter interactions including photon interactions (photoelectric effect,
                            Compton scattering, pair production) and charged particle interactions. Essential for understanding
                            detector physics and radiation protection principles.
                        </p>

                        <div class="equation-preview">
                            <strong>Klein-Nishina Formula:</strong> dσ/dΩ = r₀²/2 × (E'/E)² × [E/E' + E'/E - sin²θ]
                        </div>

                        <div class="chapter-features">
                            <div class="feature-section">
                                <h4 class="feature-title">
                                    <i class="fas fa-chart-line"></i>
                                    Figures & Visualizations
                                </h4>
                                <ul class="feature-list">
                                    <li>Photon interaction cross-sections vs. energy</li>
                                    <li>Compton scattering angular distributions</li>
                                    <li>Photoelectric absorption edges</li>
                                    <li>Pair production threshold diagrams</li>
                                    <li>Bethe-Bloch energy loss curves</li>
                                    <li>Range-energy relationships</li>
                                </ul>
                            </div>

                            <div class="feature-section">
                                <h4 class="feature-title">
                                    <i class="fas fa-table"></i>
                                    Tables & Data
                                </h4>
                                <ul class="feature-list">
                                    <li>Mass attenuation coefficients</li>
                                    <li>Absorption edge energies</li>
                                    <li>Stopping power data</li>
                                    <li>Range calculations for particles</li>
                                    <li>Interaction probability tables</li>
                                </ul>
                            </div>

                            <div class="feature-section">
                                <h4 class="feature-title">
                                    <i class="fas fa-quote-right"></i>
                                    Key Citations
                                </h4>
                                <ul class="feature-list">
                                    <li>Klein, O. & Nishina, Y. (1929). "Compton scattering"</li>
                                    <li>Bethe, H. & Bloch, F. (1933). "Stopping power"</li>
                                    <li>Heitler, W. (1954). "Quantum theory of radiation"</li>
                                    <li>Evans, R.D. (1955). "Photon interactions"</li>
                                    <li>Hubbell, J.H. (1982). "Photon cross sections"</li>
                                </ul>
                            </div>

                            <div class="feature-section">
                                <h4 class="feature-title">
                                    <i class="fas fa-calculator"></i>
                                    Interactive Tools
                                </h4>
                                <ul class="feature-list">
                                    <li>Cross-section calculator</li>
                                    <li>Attenuation coefficient finder</li>
                                    <li>Compton scattering simulator</li>
                                    <li>Energy loss calculator</li>
                                    <li>Range-energy converter</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="chapter-stats">
                        <div class="stats-group">
                            <div class="stat-item">
                                <i class="fas fa-image stat-icon"></i>
                                <span>28 Figures</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-table stat-icon"></i>
                                <span>15 Tables</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-calculator stat-icon"></i>
                                <span>42 Equations</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-quote-right stat-icon"></i>
                                <span>68 Citations</span>
                            </div>
                        </div>

                        <div class="chapter-actions">
                            <a href="../nuclear_medicine_book.html#chapter3" class="action-btn btn-primary">
                                <i class="fas fa-play"></i>
                                Start Chapter
                            </a>
                            <a href="#" class="action-btn btn-secondary">
                                <i class="fas fa-download"></i>
                                Download PDF
                            </a>
                        </div>
                    </div>
                </article>
            </section>
        </div>
    </main>

    <script>
        // MathJax Configuration
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            }
        };

        // Animation on scroll
        document.addEventListener('DOMContentLoaded', function() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.animationDelay = '0.1s';
                        entry.target.classList.add('animate__fadeInUp');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.chapter-card').forEach(card => {
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
