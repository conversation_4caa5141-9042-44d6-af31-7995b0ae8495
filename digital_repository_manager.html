<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>إدارة المستودع الرقمي - مشروع تأليف الكتب العلمية</title>
    <link rel="stylesheet" href="css/main.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet" />
    <meta name="description" content="نظام إدارة المستودع الرقمي لحفظ وتنظيم محتوى كتب الفيزياء الطبية والطب النووي" />
</head>

<body class="bg-background text-text-primary">
    <!-- Header -->
    <header class="bg-primary text-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center space-x-4 space-x-reverse">
                    <a href="book_authoring_hub.html" class="flex items-center space-x-2 space-x-reverse">
                        <div class="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <span class="font-cairo font-semibold">العودة للمشروع</span>
                    </a>
                </div>
                <nav class="hidden md:flex space-x-8 space-x-reverse">
                    <a href="#repository-overview" class="hover:text-primary-200 transition-colors">نظرة عامة</a>
                    <a href="#file-management" class="hover:text-primary-200 transition-colors">إدارة الملفات</a>
                    <a href="#backup-system" class="hover:text-primary-200 transition-colors">النسخ الاحتياطية</a>
                    <a href="#access-control" class="hover:text-primary-200 transition-colors">التحكم في الوصول</a>
                </nav>
                <div class="text-right">
                    <p class="text-sm font-cairo font-semibold">المستودع الرقمي</p>
                    <p class="text-sm text-primary-200">إدارة المحتوى</p>
                </div>
            </div>
        </div>
    </header>

    <!-- Repository Dashboard -->
    <section class="py-8 bg-surface">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="mb-8">
                <h1 class="text-3xl font-cairo font-bold text-primary mb-2">إدارة المستودع الرقمي</h1>
                <p class="text-text-secondary">نظام شامل لحفظ وإدارة محتوى مشروع تأليف الكتب العلمية</p>
            </div>

            <!-- Quick Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-2xl font-bold text-gray-900">2.4 GB</p>
                            <p class="text-sm text-gray-600">المساحة المستخدمة</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 2V6a1 1 0 112 0v1a1 1 0 11-2 0zm3 0V6a1 1 0 112 0v1a1 1 0 11-2 0z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-2xl font-bold text-gray-900">1,247</p>
                            <p class="text-sm text-gray-600">إجمالي الملفات</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-2xl font-bold text-gray-900">156</p>
                            <p class="text-sm text-gray-600">إصدارات محفوظة</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-2xl font-bold text-gray-900">99.9%</p>
                            <p class="text-sm text-gray-600">معدل التوفر</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Storage Usage -->
            <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200 mb-8">
                <h3 class="text-lg font-cairo font-bold text-primary mb-4">استخدام المساحة التخزينية</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">المساحة المستخدمة</span>
                        <span class="text-sm text-gray-500">2.4 GB من 5 GB</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-3">
                        <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-500" style="width: 48%"></div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                        <div class="text-center">
                            <div class="text-lg font-bold text-blue-600">1.2 GB</div>
                            <div class="text-sm text-gray-600">مستندات نصية</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-green-600">0.8 GB</div>
                            <div class="text-sm text-gray-600">صور ورسوم</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-purple-600">0.4 GB</div>
                            <div class="text-sm text-gray-600">ملفات أخرى</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- File Management -->
    <section id="file-management" class="py-16 bg-background">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-cairo font-bold text-primary mb-4">إدارة الملفات</h2>
                <p class="text-text-secondary max-w-3xl mx-auto">
                    تصفح وإدارة جميع ملفات المشروع مع إمكانيات البحث والتصفية المتقدمة
                </p>
            </div>

            <!-- File Browser Controls -->
            <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200 mb-8">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="relative">
                            <input type="text" placeholder="البحث في الملفات..." class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                            </div>
                        </div>
                        <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option>جميع الكتب</option>
                            <option>أساسيات الطب النووي</option>
                            <option>فيزياء التصوير النووي</option>
                            <option>الحماية من الإشعاع</option>
                        </select>
                        <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option>جميع الأنواع</option>
                            <option>مستندات نصية</option>
                            <option>صور</option>
                            <option>جداول</option>
                        </select>
                    </div>
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <button class="btn-secondary text-sm">رفع ملف جديد</button>
                        <button class="btn-primary text-sm">إنشاء مجلد</button>
                    </div>
                </div>
            </div>

            <!-- File List -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-cairo font-semibold text-gray-900">الملفات والمجلدات</h3>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <button class="p-2 text-gray-400 hover:text-gray-600">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                                </svg>
                            </button>
                            <button class="p-2 text-gray-400 hover:text-gray-600">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="divide-y divide-gray-200">
                    <!-- Folder Item -->
                    <div class="px-6 py-4 hover:bg-gray-50 cursor-pointer">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3 space-x-reverse">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"/>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">أساسيات الطب النووي</p>
                                    <p class="text-sm text-gray-500">15 ملف • آخر تحديث منذ يوم</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <span class="text-sm text-gray-500">245 MB</span>
                                <button class="p-1 text-gray-400 hover:text-gray-600">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- File Item -->
                    <div class="px-6 py-4 hover:bg-gray-50 cursor-pointer">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3 space-x-reverse">
                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">الفصل الأول - مقدمة في الطب النووي.docx</p>
                                    <p class="text-sm text-gray-500">مستند Word • آخر تحديث منذ 3 أيام</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <span class="text-sm text-gray-500">2.1 MB</span>
                                <button class="p-1 text-gray-400 hover:text-gray-600">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Image File Item -->
                    <div class="px-6 py-4 hover:bg-gray-50 cursor-pointer">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3 space-x-reverse">
                                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">رسم توضيحي - كاميرا جاما.png</p>
                                    <p class="text-sm text-gray-500">صورة PNG • آخر تحديث منذ أسبوع</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <span class="text-sm text-gray-500">856 KB</span>
                                <button class="p-1 text-gray-400 hover:text-gray-600">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <p class="text-sm text-gray-700">
                            عرض <span class="font-medium">1</span> إلى <span class="font-medium">10</span> من <span class="font-medium">1,247</span> ملف
                        </p>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <button class="px-3 py-1 text-sm text-gray-500 hover:text-gray-700">السابق</button>
                            <button class="px-3 py-1 text-sm bg-primary text-white rounded">1</button>
                            <button class="px-3 py-1 text-sm text-gray-500 hover:text-gray-700">2</button>
                            <button class="px-3 py-1 text-sm text-gray-500 hover:text-gray-700">3</button>
                            <button class="px-3 py-1 text-sm text-gray-500 hover:text-gray-700">التالي</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Backup System -->
    <section id="backup-system" class="py-16 bg-surface">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-cairo font-bold text-primary mb-4">نظام النسخ الاحتياطية</h2>
                <p class="text-text-secondary max-w-3xl mx-auto">
                    نظام متقدم للنسخ الاحتياطية التلقائية لضمان أمان وحماية جميع محتويات المشروع
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Backup Status -->
                <div class="space-y-6">
                    <div class="card">
                        <h3 class="text-xl font-cairo font-bold text-primary mb-6">حالة النسخ الاحتياطية</h3>

                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
                                <div class="flex items-center space-x-3 space-x-reverse">
                                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="font-cairo font-semibold text-green-800">النسخة الاحتياطية اليومية</p>
                                        <p class="text-sm text-green-600">مكتملة بنجاح</p>
                                    </div>
                                </div>
                                <span class="text-sm text-green-600 font-semibold">منذ ساعة</span>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
                                <div class="flex items-center space-x-3 space-x-reverse">
                                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="font-cairo font-semibold text-blue-800">النسخة الاحتياطية الأسبوعية</p>
                                        <p class="text-sm text-blue-600">مجدولة للتنفيذ</p>
                                    </div>
                                </div>
                                <span class="text-sm text-blue-600 font-semibold">غداً 2:00 ص</span>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-purple-50 rounded-lg border border-purple-200">
                                <div class="flex items-center space-x-3 space-x-reverse">
                                    <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="font-cairo font-semibold text-purple-800">النسخة الاحتياطية الشهرية</p>
                                        <p class="text-sm text-purple-600">مكتملة</p>
                                    </div>
                                </div>
                                <span class="text-sm text-purple-600 font-semibold">منذ 5 أيام</span>
                            </div>
                        </div>

                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-cairo font-semibold text-gray-700">إجمالي النسخ المحفوظة</span>
                                <span class="text-lg font-bold text-primary">156 نسخة</span>
                            </div>
                            <div class="flex items-center justify-between mt-2">
                                <span class="text-sm text-gray-600">مساحة النسخ الاحتياطية</span>
                                <span class="text-sm font-semibold text-gray-700">12.8 GB</span>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3 class="text-lg font-cairo font-bold text-primary mb-4">إعدادات النسخ الاحتياطية</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-cairo font-semibold text-gray-700">النسخ التلقائي اليومي</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                                </label>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-cairo font-semibold text-gray-700">النسخ التلقائي الأسبوعي</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                                </label>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-cairo font-semibold text-gray-700">النسخ التلقائي الشهري</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                                </label>
                            </div>
                        </div>
                        <div class="mt-6">
                            <button class="btn-primary w-full">إنشاء نسخة احتياطية الآن</button>
                        </div>
                    </div>
                </div>

                <!-- Backup History -->
                <div class="space-y-6">
                    <div class="card">
                        <h3 class="text-lg font-cairo font-bold text-primary mb-4">سجل النسخ الاحتياطية</h3>
                        <div class="space-y-3 max-h-96 overflow-y-auto">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-cairo font-semibold text-gray-800">نسخة احتياطية يومية</p>
                                    <p class="text-xs text-gray-600">2025-01-16 02:00:00</p>
                                </div>
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">مكتملة</span>
                                    <button class="text-xs text-blue-600 hover:text-blue-800">استعادة</button>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-cairo font-semibold text-gray-800">نسخة احتياطية يومية</p>
                                    <p class="text-xs text-gray-600">2025-01-15 02:00:00</p>
                                </div>
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">مكتملة</span>
                                    <button class="text-xs text-blue-600 hover:text-blue-800">استعادة</button>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-cairo font-semibold text-gray-800">نسخة احتياطية أسبوعية</p>
                                    <p class="text-xs text-gray-600">2025-01-12 02:00:00</p>
                                </div>
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">مكتملة</span>
                                    <button class="text-xs text-blue-600 hover:text-blue-800">استعادة</button>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-cairo font-semibold text-gray-800">نسخة احتياطية يومية</p>
                                    <p class="text-xs text-gray-600">2025-01-14 02:00:00</p>
                                </div>
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">مكتملة</span>
                                    <button class="text-xs text-blue-600 hover:text-blue-800">استعادة</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3 class="text-lg font-cairo font-bold text-primary mb-4">مواقع التخزين</h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                                <div class="flex items-center space-x-3 space-x-reverse">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm font-cairo font-semibold text-blue-800">الخادم المحلي</p>
                                        <p class="text-xs text-blue-600">التخزين الأساسي</p>
                                    </div>
                                </div>
                                <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">متصل</span>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg border border-purple-200">
                                <div class="flex items-center space-x-3 space-x-reverse">
                                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm font-cairo font-semibold text-purple-800">التخزين السحابي</p>
                                        <p class="text-xs text-purple-600">النسخ الاحتياطية الخارجية</p>
                                    </div>
                                </div>
                                <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">متصل</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-primary-900 text-white py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="font-cairo font-semibold mb-2">المستودع الرقمي</h3>
                    <p class="text-primary-200 text-sm">مشروع تأليف كتب الفيزياء الطبية والطب النووي</p>
                </div>
                <div class="text-right">
                    <p class="text-primary-200 text-sm">© 2025 د. محمد يعقوب إسماعيل يعقوب</p>
                    <p class="text-primary-200 text-sm">جامعة السودان للعلوم والتكنولوجيا</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // File search functionality
        document.querySelector('input[placeholder="البحث في الملفات..."]').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            // Here you would implement the actual search functionality
            console.log('Searching for:', searchTerm);
        });

        // Toggle switches for backup settings
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                console.log('Backup setting changed:', this.checked);
                // Here you would save the setting to the server
            });
        });

        // Auto-refresh backup status every 30 seconds
        setInterval(function() {
            // Here you would fetch the latest backup status from the server
            console.log('Refreshing backup status...');
        }, 30000);

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
