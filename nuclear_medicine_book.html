<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nuclear Medicine Physics & Biomedical Engineering - Dr. <PERSON></title>
    <meta name="description" content="Comprehensive Nuclear Medicine Physics and Biomedical Engineering educational module with interactive presentations, animations, and academic content">
    <meta name="keywords" content="Nuclear Medicine, Medical Physics, Biomedical Engineering, SUST, Dr <PERSON>">
    <meta name="author" content="Dr. <PERSON>, SUST-BME">
    <link rel="canonical" href="https://nuclear-medicine-physics.edu">
    <link rel="icon" type="image/x-icon" href="/public/favicon.ico">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Nuclear Medicine Physics & Biomedical Engineering">
    <meta property="og:description" content="Academic educational module by Dr. <PERSON>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://nuclear-medicine-physics.edu">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Animation Libraries -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Chart.js for interactive graphs -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- MathJax for mathematical equations -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>

    <!-- Three.js for 3D animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        /* CSS Custom Properties for Theme Management */
        :root {
            --primary-color: #2563eb;
            --secondary-color: #7c3aed;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-heavy: 0 20px 25px rgba(0, 0, 0, 0.1);
            --border-radius: 0.75rem;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            --font-mono: 'JetBrains Mono', 'Courier New', monospace;
        }

        /* Reset and Base Styles */
        *, *::before, *::after {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            font-size: 16px;
        }

        body {
            font-family: var(--font-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
            background-attachment: fixed;
            color: var(--text-primary);
            line-height: 1.7;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* Container and Layout */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        /* Header Styles */
        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 3rem 2rem;
            text-align: center;
            margin: 2rem 0;
            box-shadow: var(--shadow-heavy);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
        }

        /* Typography */
        h1 {
            font-size: clamp(2rem, 5vw, 3.5rem);
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 1rem;
            line-height: 1.2;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h2 {
            font-size: clamp(1.5rem, 4vw, 2.5rem);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
            line-height: 1.3;
        }

        h3 {
            font-size: clamp(1.25rem, 3vw, 2rem);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.75rem;
        }

        .author {
            font-size: 1.125rem;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .author-title {
            font-size: 1rem;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
        }

        .copyright {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-top: 1rem;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.05);
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-color);
        }

        .description {
            font-size: 1.125rem;
            color: var(--text-secondary);
            max-width: 900px;
            margin: 2rem auto 0;
            line-height: 1.8;
        }

        /* Main Content Containers */
        .book-container {
            background: rgba(255, 255, 255, 0.98);
            border-radius: var(--border-radius);
            padding: 2.5rem;
            box-shadow: var(--shadow-heavy);
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .book-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        }

        /* Navigation and Table of Contents */
        .navigation {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            position: sticky;
            top: 2rem;
            z-index: 100;
        }

        .nav-toggle {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            margin-bottom: 1rem;
        }

        .nav-menu {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            justify-content: center;
        }

        .nav-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.75rem 1.5rem;
            border-radius: 2rem;
            cursor: pointer;
            transition: var(--transition);
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-weight: 500;
        }

        .nav-item:hover, .nav-item.active {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .table-of-contents {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .toc-title {
            font-size: 1.8em;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .part {
            margin-bottom: 25px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            transition: transform 0.3s ease;
        }

        .part:hover {
            transform: translateY(-5px);
        }

        .part-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #fff;
        }

        .chapter {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .chapter:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(10px);
        }

        .chapter-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .chapter-content {
            font-size: 0.95em;
            opacity: 0.9;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            transform: translateY(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-10px);
        }

        .feature-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .feature-title {
            font-size: 1.3em;
            margin-bottom: 10px;
        }

        .feature-description {
            font-size: 0.95em;
            opacity: 0.9;
        }

        .interactive-section {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: center;
        }

        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            position: relative;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #333;
        }

        .qa-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
        }

        .question {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .answer {
            color: #34495e;
            padding: 10px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
        }

        .floating-atoms {
            position: fixed;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .atom {
            position: absolute;
            width: 20px;
            height: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s infinite ease-in-out;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 2em;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="floating-atoms"></div>
    
    <div class="container">
        <header class="animate__animated animate__fadeInDown">
            <h1>Nuclear Medicine Physics & Biomedical Engineering</h1>
            <div class="author">Dr. Mohammed Yagoub Esmail</div>
            <div class="author-title">Associate Professor of Biomedical Engineering</div>
            <div class="author-title">Vice Dean, College of Engineering</div>
            <div class="author-title">Sudan University of Science and Technology (SUST)</div>

            <div class="copyright">
                <strong>© 2025 Dr. Mohammed Yagoub Esmail</strong><br>
                Email: <EMAIL><br>
                Phone: +249912867327, +966538076790<br>
                All rights reserved. No part of this publication may be reproduced without permission.
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <div class="description">
                A comprehensive academic resource combining Nuclear Medicine Physics with Biomedical Engineering principles.
                This interactive educational module features detailed theoretical content, practical applications,
                animated presentations, and step-by-step learning modules designed for advanced undergraduate and graduate students.
                The content integrates cutting-edge research with clinical applications, providing a thorough understanding
                of nuclear medicine technologies and their biomedical engineering foundations.
            </div>
        </header>

        <!-- Navigation Menu -->
        <nav class="navigation animate__animated animate__fadeInUp">
            <button class="nav-toggle" id="navToggle">
                <i class="fas fa-bars"></i>
            </button>
            <div class="nav-menu" id="navMenu">
                <div class="nav-item active" data-section="overview">
                    <i class="fas fa-home"></i> Overview
                </div>
                <div class="nav-item" data-section="chapters">
                    <i class="fas fa-book"></i> Chapters
                </div>
                <div class="nav-item" data-section="presentations">
                    <i class="fas fa-presentation"></i> Presentations
                </div>
                <div class="nav-item" data-section="interactive">
                    <i class="fas fa-atom"></i> Interactive Tools
                </div>
                <div class="nav-item" data-section="references">
                    <i class="fas fa-citation"></i> References
                </div>
                <div class="nav-item" data-section="resources">
                    <i class="fas fa-download"></i> Resources
                </div>
            </div>
        </nav>

        <!-- Main Content Sections -->
        <div class="content-sections">
            <!-- Overview Section -->
            <section id="overview" class="content-section active">
                <div class="book-container animate__animated animate__fadeIn">
                    <h2><i class="fas fa-atom"></i> Course Overview</h2>
                    <div class="overview-grid">
                        <div class="overview-card">
                            <h3><i class="fas fa-graduation-cap"></i> Learning Objectives</h3>
                            <ul>
                                <li>Understand fundamental principles of nuclear medicine physics</li>
                                <li>Master biomedical engineering applications in nuclear medicine</li>
                                <li>Analyze radiation detection and imaging systems</li>
                                <li>Evaluate safety protocols and radiation protection</li>
                                <li>Apply quality control procedures in clinical settings</li>
                            </ul>
                        </div>
                        <div class="overview-card">
                            <h3><i class="fas fa-users"></i> Target Audience</h3>
                            <ul>
                                <li>Graduate students in Biomedical Engineering</li>
                                <li>Medical Physics residents and professionals</li>
                                <li>Nuclear medicine technologists</li>
                                <li>Healthcare professionals in imaging</li>
                                <li>Research scientists in medical imaging</li>
                            </ul>
                        </div>
                        <div class="overview-card">
                            <h3><i class="fas fa-clock"></i> Course Duration</h3>
                            <ul>
                                <li>Total Duration: 16 weeks (1 semester)</li>
                                <li>Lecture Hours: 48 hours</li>
                                <li>Laboratory Sessions: 32 hours</li>
                                <li>Independent Study: 80 hours</li>
                                <li>Assessment: Continuous + Final Exam</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Chapters Section -->
            <section id="chapters" class="content-section">
                <div class="book-container">
                    <h2><i class="fas fa-book-open"></i> Comprehensive Chapter Outline</h2>

                    <!-- Part I: Fundamental Physics -->
                    <div class="part-container">
                        <div class="part-header">
                            <h3><i class="fas fa-atom"></i> Part I: Nuclear Physics Fundamentals</h3>
                            <p>Foundation concepts in atomic and nuclear physics essential for nuclear medicine</p>
                        </div>

                        <div class="chapter-grid">
                            <div class="chapter-card" data-chapter="1">
                                <div class="chapter-number">01</div>
                                <div class="chapter-content">
                                    <h4>Atomic Structure and Nuclear Properties</h4>
                                    <p class="chapter-description">
                                        Comprehensive study of atomic structure, nuclear composition, and fundamental properties.
                                        <strong>Key Topics:</strong> Atomic models, nuclear binding energy, mass-energy equivalence.
                                    </p>
                                    <div class="chapter-features">
                                        <span class="feature-tag"><i class="fas fa-chart-line"></i> 15 Figures</span>
                                        <span class="feature-tag"><i class="fas fa-table"></i> 8 Tables</span>
                                        <span class="feature-tag"><i class="fas fa-calculator"></i> 25 Equations</span>
                                        <span class="feature-tag"><i class="fas fa-quote-right"></i> 45 Citations</span>
                                    </div>
                                    <div class="learning-outcomes">
                                        <h5>Learning Outcomes:</h5>
                                        <ul>
                                            <li>Analyze atomic structure using quantum mechanical principles</li>
                                            <li>Calculate nuclear binding energies and stability</li>
                                            <li>Evaluate nuclear properties relevant to medical applications</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                <div class="part" onclick="showChapter('part2')">
                    <div class="part-title">الجزء الثاني: رحلة المريض خطوة بخطوة</div>
                    <div class="chapter" onclick="event.stopPropagation(); showChapter('chapter3')">
                        <div class="chapter-title">الفصل الثالث: رحلة التشخيص</div>
                        <div class="chapter-content">كيف نرى المرض من الداخل؟</div>
                    </div>
                    <div class="chapter" onclick="event.stopPropagation(); showChapter('chapter4')">
                        <div class="chapter-title">الفصل الرابع: رحلة العلاج</div>
                        <div class="chapter-content">الرصاصة السحرية التي تستهدف السرطان</div>
                    </div>
                </div>

                <div class="part" onclick="showChapter('part3')">
                    <div class="part-title">الجزء الثالث: هل هو آمن؟</div>
                    <div class="chapter" onclick="event.stopPropagation(); showChapter('chapter5')">
                        <div class="chapter-title">الفصل الخامس: الجرعة الإشعاعية</div>
                        <div class="chapter-content">أقل مما تتوقع!</div>
                    </div>
                    <div class="chapter" onclick="event.stopPropagation(); showChapter('chapter6')">
                        <div class="chapter-title">الفصل السادس: جيش من الخبراء</div>
                        <div class="chapter-content">في خدمتك</div>
                    </div>
                </div>

                <div class="part" onclick="showChapter('part4')">
                    <div class="part-title">الجزء الرابع: نظرة إلى المستقبل</div>
                    <div class="chapter" onclick="event.stopPropagation(); showChapter('chapter7')">
                        <div class="chapter-title">الفصل السابع: طب أكثر دقة وشخصية</div>
                        <div class="chapter-content">دور الذكاء الاصطناعي والتطوير المستقبلي</div>
                    </div>
                </div>
            </div>

            <div class="features">
                <div class="feature-card">
                    <div class="feature-icon">🔬</div>
                    <div class="feature-title">لغة مبسطة</div>
                    <div class="feature-description">شرح المفاهيم المعقدة بأسلوب قصصي سهل الفهم</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <div class="feature-title">تشبيهات واقعية</div>
                    <div class="feature-description">استخدام الجواسيس الطبية والرصاصات السحرية</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">❤️</div>
                    <div class="feature-title">قصص إنسانية</div>
                    <div class="feature-description">تجارب حقيقية ملهمة لمرضى تم شفاؤهم</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🛡️</div>
                    <div class="feature-title">الأمان أولاً</div>
                    <div class="feature-description">شرح مفصل لإجراءات الأمان والحماية</div>
                </div>
            </div>

            <div class="interactive-section">
                <h3>اكتشف المزيد</h3>
                <p>تفاعل مع محتوى الكتاب واكتشف عالم الطب النووي</p>
                <button class="btn" onclick="showModal('faq')">الأسئلة الشائعة</button>
                <button class="btn" onclick="showModal('glossary')">مسرد المصطلحات</button>
                <button class="btn" onclick="showModal('safety')">معلومات الأمان</button>
            </div>
        </div>
    </div>

    <!-- Modal for FAQ -->
    <div id="faqModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>الأسئلة الشائعة</h2>
            <div class="qa-section">
                <div class="question">هل سأصبح مشعاً بعد الفحص؟</div>
                <div class="answer">لا، إطلاقاً. المادة المشعة المستخدمة في الطب النووي تكون بكميات ضئيلة جداً وتختفي من الجسم بسرعة. لن تصبح مشعاً ولن تشكل خطراً على الآخرين.</div>
            </div>
            <div class="qa-section">
                <div class="question">هل يمكنني الاقتراب من الأطفال بعد الفحص؟</div>
                <div class="answer">نعم، في معظم الحالات يمكنك العودة إلى حياتك الطبيعية فوراً. في حالات نادرة جداً قد ينصح الطبيب بتجنب الاقتراب من الأطفال والحوامل لفترة قصيرة.</div>
            </div>
            <div class="qa-section">
                <div class="question">هل الفحص مؤلم؟</div>
                <div class="answer">لا، الفحص غير مؤلم تماماً. ستشعر فقط بوخزة بسيطة عند الحقن، مثل أي حقنة عادية، ثم لن تشعر بأي شيء.</div>
            </div>
        </div>
    </div>

    <!-- Modal for Glossary -->
    <div id="glossaryModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>مسرد المصطلحات</h2>
            <div class="qa-section">
                <div class="question">الجواسيس الطبية</div>
                <div class="answer">مواد مشعة آمنة تُحقن في الجسم وتذهب إلى الأعضاء المطلوب فحصها لتعطي صورة واضحة عن وظائفها.</div>
            </div>
            <div class="qa-section">
                <div class="question">الرصاصة السحرية</div>
                <div class="answer">دواء مشع علاجي يستهدف الخلايا السرطانية بدقة عالية ويدمرها دون إلحاق ضرر كبير بالخلايا السليمة.</div>
            </div>
            <div class="qa-section">
                <div class="question">PET/CT</div>
                <div class="answer">جهاز تصوير متطور يجمع بين التصوير الوظيفي والتشريحي ليعطي صورة شاملة عن الجسم.</div>
            </div>
        </div>
    </div>

    <!-- Modal for Safety -->
    <div id="safetyModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>معلومات الأمان</h2>
            <div class="qa-section">
                <div class="question">مبدأ ALARA</div>
                <div class="answer">يعني "أقل ما يمكن تحقيقه بشكل معقول" - الأطباء يستخدمون أقل جرعة إشعاع ممكنة للحصول على المعلومة المطلوبة.</div>
            </div>
            <div class="qa-section">
                <div class="question">مقارنة الجرعات</div>
                <div class="answer">جرعة الإشعاع من فحص طبي نووي تعادل ما نتعرض له طبيعياً خلال بضع سنوات، أو عدة رحلات طيران طويلة.</div>
            </div>
            <div class="qa-section">
                <div class="question">فريق الخبراء</div>
                <div class="answer">يشرف على كل فحص فريق من الخبراء: الطبيب النووي، الفيزيائي الطبي، الكيميائي الإشعاعي، والتقني المتخصص.</div>
            </div>
        </div>
    </div>

    <script>
        // Create floating atoms animation
        function createFloatingAtoms() {
            const atomsContainer = document.querySelector('.floating-atoms');
            for (let i = 0; i < 20; i++) {
                const atom = document.createElement('div');
                atom.className = 'atom';
                atom.style.left = Math.random() * 100 + '%';
                atom.style.top = Math.random() * 100 + '%';
                atom.style.animationDelay = Math.random() * 6 + 's';
                atom.style.animationDuration = (Math.random() * 4 + 4) + 's';
                atomsContainer.appendChild(atom);
            }
        }

        // Progress bar animation
        function animateProgress() {
            const progressFill = document.getElementById('progressFill');
            let width = 0;
            const interval = setInterval(() => {
                if (width >= 100) {
                    clearInterval(interval);
                } else {
                    width += 2;
                    progressFill.style.width = width + '%';
                }
            }, 50);
        }

        // Modal functions
        function showModal(type) {
            const modal = document.getElementById(type + 'Modal');
            modal.style.display = 'block';
        }

        function closeModal(modal) {
            modal.style.display = 'none';
        }

        // Chapter navigation
        function showChapter(chapterId) {
            // This could be expanded to show chapter content
            alert('عذراً، محتوى الفصول سيكون متاحاً في النسخة الكاملة من الكتاب!');
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            createFloatingAtoms();
            animateProgress();

            // Close modal events
            document.querySelectorAll('.close').forEach(closeBtn => {
                closeBtn.addEventListener('click', function() {
                    this.closest('.modal').style.display = 'none';
                });
            });

            // Close modal when clicking outside
            document.querySelectorAll('.modal').forEach(modal => {
                modal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        this.style.display = 'none';
                    }
                });
            });

            // Smooth scrolling for better UX
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });
        });

        // Add some interactive effects
        document.querySelectorAll('.chapter').forEach(chapter => {
            chapter.addEventListener('mouseenter', function() {
                this.style.boxShadow = '0 10px 20px rgba(0,0,0,0.2)';
            });
            
            chapter.addEventListener('mouseleave', function() {
                this.style.boxShadow = 'none';
            });
        });
    </script>
</body>
</html>