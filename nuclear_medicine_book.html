<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الطب النووي: كيف تُنقذ الذرة حياة الإنسان</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            font-size: 2.5em;
            color: #fff;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .author {
            font-size: 1.2em;
            color: #e8e8e8;
            margin-bottom: 10px;
        }

        .description {
            font-size: 1.1em;
            color: #f0f0f0;
            max-width: 800px;
            margin: 0 auto;
            margin-top: 20px;
        }

        .book-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .table-of-contents {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .toc-title {
            font-size: 1.8em;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .part {
            margin-bottom: 25px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            transition: transform 0.3s ease;
        }

        .part:hover {
            transform: translateY(-5px);
        }

        .part-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #fff;
        }

        .chapter {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .chapter:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(10px);
        }

        .chapter-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .chapter-content {
            font-size: 0.95em;
            opacity: 0.9;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            transform: translateY(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-10px);
        }

        .feature-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .feature-title {
            font-size: 1.3em;
            margin-bottom: 10px;
        }

        .feature-description {
            font-size: 0.95em;
            opacity: 0.9;
        }

        .interactive-section {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: center;
        }

        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            position: relative;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #333;
        }

        .qa-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
        }

        .question {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .answer {
            color: #34495e;
            padding: 10px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
        }

        .floating-atoms {
            position: fixed;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .atom {
            position: absolute;
            width: 20px;
            height: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s infinite ease-in-out;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 2em;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="floating-atoms"></div>
    
    <div class="container">
        <header>
            <h1>الطب النووي: كيف تُنقذ الذرة حياة الإنسان</h1>
            <div class="author">د. محمد يعقوب إسماعيل يعقوب</div>
            <div class="author">نائب عميد كلية الهندسة - أستاذ مساعد الهندسة الطبية الحيوية</div>
            <div class="author">جامعة السودان للعلوم والتكنولوجيا</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="description">
                كتاب توعوي يأخذ القارئ في رحلة شيقة ومبسطة إلى عالم الطب النووي، ليكشف عن الوجه الإيجابي والمُنقذ للحياة للتطبيقات النووية. باستخدام لغة سهلة وقصص إنسانية ملهمة، يهدف الكتاب إلى إزالة الغموض والخوف المحيط بكلمة "نووي".
            </div>
        </header>

        <div class="book-container">
            <div class="table-of-contents">
                <h2 class="toc-title">فهرس المحتويات</h2>
                
                <div class="part" onclick="showChapter('part1')">
                    <div class="part-title">الجزء الأول: الذرة الصديقة</div>
                    <div class="chapter" onclick="event.stopPropagation(); showChapter('chapter1')">
                        <div class="chapter-title">الفصل الأول: ذرة للشفاء، لا للحرب</div>
                        <div class="chapter-content">مقدمة تزيل الربط الذهني بين "النووي" والأسلحة</div>
                    </div>
                    <div class="chapter" onclick="event.stopPropagation(); showChapter('chapter2')">
                        <div class="chapter-title">الفصل الثاني: ما هو الطب النووي؟</div>
                        <div class="chapter-content">شرح مبدأ الطب النووي باستخدام تشبيه "الجاسوس"</div>
                    </div>
                </div>

                <div class="part" onclick="showChapter('part2')">
                    <div class="part-title">الجزء الثاني: رحلة المريض خطوة بخطوة</div>
                    <div class="chapter" onclick="event.stopPropagation(); showChapter('chapter3')">
                        <div class="chapter-title">الفصل الثالث: رحلة التشخيص</div>
                        <div class="chapter-content">كيف نرى المرض من الداخل؟</div>
                    </div>
                    <div class="chapter" onclick="event.stopPropagation(); showChapter('chapter4')">
                        <div class="chapter-title">الفصل الرابع: رحلة العلاج</div>
                        <div class="chapter-content">الرصاصة السحرية التي تستهدف السرطان</div>
                    </div>
                </div>

                <div class="part" onclick="showChapter('part3')">
                    <div class="part-title">الجزء الثالث: هل هو آمن؟</div>
                    <div class="chapter" onclick="event.stopPropagation(); showChapter('chapter5')">
                        <div class="chapter-title">الفصل الخامس: الجرعة الإشعاعية</div>
                        <div class="chapter-content">أقل مما تتوقع!</div>
                    </div>
                    <div class="chapter" onclick="event.stopPropagation(); showChapter('chapter6')">
                        <div class="chapter-title">الفصل السادس: جيش من الخبراء</div>
                        <div class="chapter-content">في خدمتك</div>
                    </div>
                </div>

                <div class="part" onclick="showChapter('part4')">
                    <div class="part-title">الجزء الرابع: نظرة إلى المستقبل</div>
                    <div class="chapter" onclick="event.stopPropagation(); showChapter('chapter7')">
                        <div class="chapter-title">الفصل السابع: طب أكثر دقة وشخصية</div>
                        <div class="chapter-content">دور الذكاء الاصطناعي والتطوير المستقبلي</div>
                    </div>
                </div>
            </div>

            <div class="features">
                <div class="feature-card">
                    <div class="feature-icon">🔬</div>
                    <div class="feature-title">لغة مبسطة</div>
                    <div class="feature-description">شرح المفاهيم المعقدة بأسلوب قصصي سهل الفهم</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <div class="feature-title">تشبيهات واقعية</div>
                    <div class="feature-description">استخدام الجواسيس الطبية والرصاصات السحرية</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">❤️</div>
                    <div class="feature-title">قصص إنسانية</div>
                    <div class="feature-description">تجارب حقيقية ملهمة لمرضى تم شفاؤهم</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🛡️</div>
                    <div class="feature-title">الأمان أولاً</div>
                    <div class="feature-description">شرح مفصل لإجراءات الأمان والحماية</div>
                </div>
            </div>

            <div class="interactive-section">
                <h3>اكتشف المزيد</h3>
                <p>تفاعل مع محتوى الكتاب واكتشف عالم الطب النووي</p>
                <button class="btn" onclick="showModal('faq')">الأسئلة الشائعة</button>
                <button class="btn" onclick="showModal('glossary')">مسرد المصطلحات</button>
                <button class="btn" onclick="showModal('safety')">معلومات الأمان</button>
            </div>
        </div>
    </div>

    <!-- Modal for FAQ -->
    <div id="faqModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>الأسئلة الشائعة</h2>
            <div class="qa-section">
                <div class="question">هل سأصبح مشعاً بعد الفحص؟</div>
                <div class="answer">لا، إطلاقاً. المادة المشعة المستخدمة في الطب النووي تكون بكميات ضئيلة جداً وتختفي من الجسم بسرعة. لن تصبح مشعاً ولن تشكل خطراً على الآخرين.</div>
            </div>
            <div class="qa-section">
                <div class="question">هل يمكنني الاقتراب من الأطفال بعد الفحص؟</div>
                <div class="answer">نعم، في معظم الحالات يمكنك العودة إلى حياتك الطبيعية فوراً. في حالات نادرة جداً قد ينصح الطبيب بتجنب الاقتراب من الأطفال والحوامل لفترة قصيرة.</div>
            </div>
            <div class="qa-section">
                <div class="question">هل الفحص مؤلم؟</div>
                <div class="answer">لا، الفحص غير مؤلم تماماً. ستشعر فقط بوخزة بسيطة عند الحقن، مثل أي حقنة عادية، ثم لن تشعر بأي شيء.</div>
            </div>
        </div>
    </div>

    <!-- Modal for Glossary -->
    <div id="glossaryModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>مسرد المصطلحات</h2>
            <div class="qa-section">
                <div class="question">الجواسيس الطبية</div>
                <div class="answer">مواد مشعة آمنة تُحقن في الجسم وتذهب إلى الأعضاء المطلوب فحصها لتعطي صورة واضحة عن وظائفها.</div>
            </div>
            <div class="qa-section">
                <div class="question">الرصاصة السحرية</div>
                <div class="answer">دواء مشع علاجي يستهدف الخلايا السرطانية بدقة عالية ويدمرها دون إلحاق ضرر كبير بالخلايا السليمة.</div>
            </div>
            <div class="qa-section">
                <div class="question">PET/CT</div>
                <div class="answer">جهاز تصوير متطور يجمع بين التصوير الوظيفي والتشريحي ليعطي صورة شاملة عن الجسم.</div>
            </div>
        </div>
    </div>

    <!-- Modal for Safety -->
    <div id="safetyModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>معلومات الأمان</h2>
            <div class="qa-section">
                <div class="question">مبدأ ALARA</div>
                <div class="answer">يعني "أقل ما يمكن تحقيقه بشكل معقول" - الأطباء يستخدمون أقل جرعة إشعاع ممكنة للحصول على المعلومة المطلوبة.</div>
            </div>
            <div class="qa-section">
                <div class="question">مقارنة الجرعات</div>
                <div class="answer">جرعة الإشعاع من فحص طبي نووي تعادل ما نتعرض له طبيعياً خلال بضع سنوات، أو عدة رحلات طيران طويلة.</div>
            </div>
            <div class="qa-section">
                <div class="question">فريق الخبراء</div>
                <div class="answer">يشرف على كل فحص فريق من الخبراء: الطبيب النووي، الفيزيائي الطبي، الكيميائي الإشعاعي، والتقني المتخصص.</div>
            </div>
        </div>
    </div>

    <script>
        // Create floating atoms animation
        function createFloatingAtoms() {
            const atomsContainer = document.querySelector('.floating-atoms');
            for (let i = 0; i < 20; i++) {
                const atom = document.createElement('div');
                atom.className = 'atom';
                atom.style.left = Math.random() * 100 + '%';
                atom.style.top = Math.random() * 100 + '%';
                atom.style.animationDelay = Math.random() * 6 + 's';
                atom.style.animationDuration = (Math.random() * 4 + 4) + 's';
                atomsContainer.appendChild(atom);
            }
        }

        // Progress bar animation
        function animateProgress() {
            const progressFill = document.getElementById('progressFill');
            let width = 0;
            const interval = setInterval(() => {
                if (width >= 100) {
                    clearInterval(interval);
                } else {
                    width += 2;
                    progressFill.style.width = width + '%';
                }
            }, 50);
        }

        // Modal functions
        function showModal(type) {
            const modal = document.getElementById(type + 'Modal');
            modal.style.display = 'block';
        }

        function closeModal(modal) {
            modal.style.display = 'none';
        }

        // Chapter navigation
        function showChapter(chapterId) {
            // This could be expanded to show chapter content
            alert('عذراً، محتوى الفصول سيكون متاحاً في النسخة الكاملة من الكتاب!');
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            createFloatingAtoms();
            animateProgress();

            // Close modal events
            document.querySelectorAll('.close').forEach(closeBtn => {
                closeBtn.addEventListener('click', function() {
                    this.closest('.modal').style.display = 'none';
                });
            });

            // Close modal when clicking outside
            document.querySelectorAll('.modal').forEach(modal => {
                modal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        this.style.display = 'none';
                    }
                });
            });

            // Smooth scrolling for better UX
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });
        });

        // Add some interactive effects
        document.querySelectorAll('.chapter').forEach(chapter => {
            chapter.addEventListener('mouseenter', function() {
                this.style.boxShadow = '0 10px 20px rgba(0,0,0,0.2)';
            });
            
            chapter.addEventListener('mouseleave', function() {
                this.style.boxShadow = 'none';
            });
        });
    </script>
</body>
</html>