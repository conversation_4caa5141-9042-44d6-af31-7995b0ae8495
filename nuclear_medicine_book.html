<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nuclear Medicine Physics & Biomedical Engineering - Dr. <PERSON></title>
    <meta name="description" content="Comprehensive Nuclear Medicine Physics and Biomedical Engineering educational module with interactive presentations, animations, and academic content">
    <meta name="keywords" content="Nuclear Medicine, Medical Physics, Biomedical Engineering, SUST, Dr <PERSON>">
    <meta name="author" content="Dr. <PERSON>, SUST-BME">
    <link rel="canonical" href="https://nuclear-medicine-physics.edu">
    <link rel="icon" type="image/x-icon" href="/public/favicon.ico">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Nuclear Medicine Physics & Biomedical Engineering">
    <meta property="og:description" content="Academic educational module by Dr. <PERSON>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://nuclear-medicine-physics.edu">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Animation Libraries -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Chart.js for interactive graphs -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- MathJax for mathematical equations -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>

    <!-- Three.js for 3D animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        /* CSS Custom Properties for Theme Management */
        :root {
            --primary-color: #2563eb;
            --secondary-color: #7c3aed;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-heavy: 0 20px 25px rgba(0, 0, 0, 0.1);
            --border-radius: 0.75rem;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            --font-mono: 'JetBrains Mono', 'Courier New', monospace;
        }

        /* Reset and Base Styles */
        *, *::before, *::after {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            font-size: 16px;
        }

        body {
            font-family: var(--font-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
            background-attachment: fixed;
            color: var(--text-primary);
            line-height: 1.7;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* Container and Layout */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        /* Header Styles */
        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 3rem 2rem;
            text-align: center;
            margin: 2rem 0;
            box-shadow: var(--shadow-heavy);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
        }

        /* Typography */
        h1 {
            font-size: clamp(2rem, 5vw, 3.5rem);
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 1rem;
            line-height: 1.2;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h2 {
            font-size: clamp(1.5rem, 4vw, 2.5rem);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
            line-height: 1.3;
        }

        h3 {
            font-size: clamp(1.25rem, 3vw, 2rem);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.75rem;
        }

        .author {
            font-size: 1.125rem;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .author-title {
            font-size: 1rem;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
        }

        .copyright {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-top: 1rem;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.05);
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-color);
        }

        .description {
            font-size: 1.125rem;
            color: var(--text-secondary);
            max-width: 900px;
            margin: 2rem auto 0;
            line-height: 1.8;
        }

        /* Main Content Containers */
        .book-container {
            background: rgba(255, 255, 255, 0.98);
            border-radius: var(--border-radius);
            padding: 2.5rem;
            box-shadow: var(--shadow-heavy);
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .book-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        }

        /* Navigation and Table of Contents */
        .navigation {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            position: sticky;
            top: 2rem;
            z-index: 100;
        }

        .nav-toggle {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            margin-bottom: 1rem;
        }

        .nav-menu {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            justify-content: center;
        }

        .nav-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.75rem 1.5rem;
            border-radius: 2rem;
            cursor: pointer;
            transition: var(--transition);
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-weight: 500;
        }

        .nav-item:hover, .nav-item.active {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        /* Content Sections */
        .content-sections {
            position: relative;
        }

        .content-section {
            display: none;
            animation: fadeIn 0.5s ease-in-out;
        }

        .content-section.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Overview Grid */
        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .overview-card {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            padding: 2rem;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-color);
            box-shadow: var(--shadow-medium);
            transition: var(--transition);
        }

        .overview-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-heavy);
        }

        .overview-card h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .overview-card ul {
            list-style: none;
            padding: 0;
        }

        .overview-card li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            position: relative;
            padding-left: 1.5rem;
        }

        .overview-card li:before {
            content: '✓';
            position: absolute;
            left: 0;
            color: var(--success-color);
            font-weight: bold;
        }

        /* Part Containers */
        .part-container {
            margin-bottom: 3rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            overflow: hidden;
            background: white;
        }

        .part-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .part-header h3 {
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        /* Chapter Grid */
        .chapter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            padding: 2rem;
        }

        .chapter-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            overflow: hidden;
            transition: var(--transition);
            cursor: pointer;
            position: relative;
        }

        .chapter-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-heavy);
            border-color: var(--primary-color);
        }

        /* Chapter Actions */
        .chapter-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .chapter-link {
            padding: 0.5rem 1rem;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            font-size: 0.9rem;
            font-weight: 500;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .chapter-link:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }

        .chapter-link.secondary {
            background: var(--light-color);
            color: var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .chapter-link.secondary:hover {
            background: var(--border-color);
        }

        .chapter-number {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            font-size: 2rem;
            font-weight: bold;
            padding: 1rem;
            text-align: center;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chapter-content {
            padding: 1.5rem;
        }

        .chapter-content h4 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-size: 1.25rem;
        }

        .chapter-description {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .chapter-features {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .feature-tag {
            background: var(--primary-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .learning-outcomes, .key-equations, .references {
            margin-top: 1rem;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.02);
            border-radius: 0.5rem;
        }

        .learning-outcomes h5, .key-equations h5, .references h5 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }

        .learning-outcomes ul, .references ul {
            list-style: none;
            padding: 0;
        }

        .learning-outcomes li, .references li {
            padding: 0.25rem 0;
            padding-left: 1rem;
            position: relative;
            font-size: 0.9rem;
        }

        .learning-outcomes li:before {
            content: '→';
            position: absolute;
            left: 0;
            color: var(--primary-color);
        }

        .equation {
            background: var(--light-color);
            padding: 0.5rem;
            border-radius: 0.25rem;
            font-family: var(--font-mono);
            font-size: 0.9rem;
            margin: 0.25rem 0;
            border-left: 3px solid var(--primary-color);
        }

        /* Presentations Styles */
        .section-description {
            color: var(--text-secondary);
            font-size: 1.125rem;
            margin-bottom: 2rem;
            text-align: center;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .presentations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .presentation-card {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-medium);
            transition: var(--transition);
            cursor: pointer;
        }

        .presentation-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-heavy);
        }

        .presentation-thumbnail {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            color: white;
        }

        .presentation-icon {
            font-size: 4rem;
            opacity: 0.3;
        }

        .play-button {
            position: absolute;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            border-radius: 50%;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            transition: var(--transition);
        }

        .play-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .presentation-content {
            padding: 1.5rem;
        }

        .presentation-content h3 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .presentation-stats {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .presentation-topics {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .topic-tag {
            background: var(--accent-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
        }

        /* Interactive Tools Styles */
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .tool-card {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-medium);
            transition: var(--transition);
        }

        .tool-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-heavy);
        }

        .tool-header {
            background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
            color: white;
            padding: 1.5rem;
            text-align: center;
        }

        .tool-icon {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .tool-content {
            padding: 1.5rem;
        }

        .tool-features {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin: 1rem 0;
        }

        .feature {
            background: var(--light-color);
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            color: var(--text-secondary);
        }

        .tool-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            width: 100%;
            font-size: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .tool-button:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }

        /* References Styles */
        .references-container {
            margin-top: 2rem;
        }

        .reference-category {
            margin-bottom: 3rem;
        }

        .reference-category h3 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .reference-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .reference-item {
            display: flex;
            gap: 1rem;
            padding: 1rem;
            background: var(--light-color);
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-color);
        }

        .reference-number {
            background: var(--primary-color);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            flex-shrink: 0;
        }

        .reference-content {
            flex: 1;
        }

        .reference-note {
            color: var(--text-secondary);
            font-style: italic;
            margin-top: 0.5rem;
            font-size: 0.9rem;
        }

        /* Resources Styles */
        .resources-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .resource-category h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .resource-items {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .resource-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
            transition: var(--transition);
        }

        .resource-item:hover {
            box-shadow: var(--shadow-medium);
        }

        .resource-item i {
            font-size: 2rem;
            color: var(--primary-color);
        }

        .resource-info {
            flex: 1;
        }

        .resource-info h4 {
            margin-bottom: 0.25rem;
            color: var(--text-primary);
        }

        .resource-info p {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .file-size {
            color: var(--text-secondary);
            font-size: 0.8rem;
            background: var(--light-color);
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }

        .download-btn {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .download-btn:hover {
            background: #059669;
            transform: translateY(-2px);
        }

        /* Advanced Interactive Features Styles */

        /* Particle Canvas */
        #particleCanvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.3;
        }

        /* Advanced Modal Styles */
        .decay-simulator-modal .modal-content,
        .dose-calculator-modal .modal-content {
            max-width: 95vw;
            width: 1200px;
            max-height: 90vh;
            overflow-y: auto;
            padding: 0;
        }

        .simulator-content,
        .calculator-content {
            padding: 2rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        /* Tab System */
        .simulator-tabs,
        .calculator-tabs {
            display: flex;
            background: white;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            margin: -2rem -2rem 2rem -2rem;
            padding: 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .tab-button {
            flex: 1;
            padding: 1rem 2rem;
            border: none;
            background: transparent;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            color: var(--text-secondary);
            transition: var(--transition);
            border-bottom: 3px solid transparent;
        }

        .tab-button:first-child {
            border-radius: var(--border-radius) 0 0 0;
        }

        .tab-button:last-child {
            border-radius: 0 var(--border-radius) 0 0;
        }

        .tab-button.active {
            color: var(--primary-color);
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.1), rgba(124, 58, 237, 0.1));
            border-bottom-color: var(--primary-color);
        }

        .tab-button:hover:not(.active) {
            background: rgba(0, 0, 0, 0.05);
            color: var(--text-primary);
        }

        .tab-content {
            display: none;
            animation: fadeInUp 0.3s ease;
        }

        .tab-content.active {
            display: block;
        }

        /* Advanced Control Groups */
        .simulator-controls,
        .calculator-inputs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            background: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-medium);
            margin-bottom: 2rem;
        }

        .control-group,
        .input-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .control-group label,
        .input-group label {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .control-group input,
        .control-group select,
        .input-group input,
        .input-group select {
            padding: 0.75rem;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: var(--transition);
            background: white;
        }

        .control-group input:focus,
        .control-group select:focus,
        .input-group input:focus,
        .input-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        /* Range Sliders */
        input[type="range"] {
            -webkit-appearance: none;
            appearance: none;
            height: 6px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 3px;
            outline: none;
            margin: 0.5rem 0;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            background: white;
            border: 3px solid var(--primary-color);
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            transition: var(--transition);
        }

        input[type="range"]::-webkit-slider-thumb:hover {
            transform: scale(1.2);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        input[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            background: white;
            border: 3px solid var(--primary-color);
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        /* Simulation Buttons */
        .simulation-buttons {
            grid-column: 1 / -1;
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 1rem;
        }

        .btn-primary,
        .btn-secondary,
        .btn-success,
        .btn-interactive {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
        }

        .btn-secondary {
            background: var(--light-color);
            color: var(--text-primary);
            border: 2px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--border-color);
            transform: translateY(-2px);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #059669);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        }

        .btn-interactive {
            background: linear-gradient(135deg, var(--accent-color), #0891b2);
            color: white;
            box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3);
        }

        .btn-interactive:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(6, 182, 212, 0.4);
        }

        /* Button disabled state */
        .btn-primary:disabled,
        .btn-secondary:disabled,
        .btn-success:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* Simulation Display */
        .simulation-display {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow-medium);
            margin-bottom: 2rem;
        }

        .chart-container {
            position: relative;
            margin-bottom: 2rem;
            padding: 1rem;
            background: #fafafa;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }

        .real-time-data {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }

        .data-panel,
        .equation-panel {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-color);
        }

        .data-panel h4,
        .equation-panel h4 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-size: 1.1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .data-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            padding: 0.75rem;
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: var(--transition);
        }

        .data-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .data-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .data-value {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--primary-color);
            font-family: var(--font-mono);
        }

        /* Equation Display */
        .equation-display {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .equation {
            background: white;
            padding: 0.75rem;
            border-radius: 0.5rem;
            font-family: var(--font-mono);
            font-size: 1rem;
            text-align: center;
            border-left: 3px solid var(--accent-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        /* Results Section */
        .simulation-results,
        .dose-results {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow-medium);
        }

        .results-tabs {
            display: flex;
            background: var(--light-color);
            border-radius: var(--border-radius);
            padding: 0.25rem;
            margin-bottom: 2rem;
        }

        .result-tab {
            flex: 1;
            padding: 0.75rem 1rem;
            border: none;
            background: transparent;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-secondary);
            border-radius: calc(var(--border-radius) - 0.25rem);
            transition: var(--transition);
        }

        .result-tab.active {
            background: white;
            color: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .results-content {
            position: relative;
        }

        .result-panel {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .result-panel.active {
            display: block;
        }

        /* Data Table */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .data-table th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }

        .data-table td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid var(--border-color);
            font-family: var(--font-mono);
            font-size: 0.9rem;
        }

        .data-table tr:hover {
            background: rgba(37, 99, 235, 0.05);
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        /* Statistics Grid */
        .stats-grid,
        .dose-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .stat-item,
        .dose-item {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            text-align: center;
            border-left: 4px solid var(--accent-color);
            transition: var(--transition);
        }

        .stat-item:hover,
        .dose-item:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-medium);
        }

        .stat-item h5,
        .dose-item .dose-label {
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-item span,
        .dose-item .dose-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            font-family: var(--font-mono);
        }

        /* Dose Visualization */
        .dose-visualization {
            margin-top: 2rem;
            padding: 1rem;
            background: var(--light-color);
            border-radius: var(--border-radius);
        }

        .results-display {
            margin-bottom: 2rem;
        }

        .results-display h4 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            text-align: center;
            font-size: 1.3rem;
        }

        /* MIRD Equations */
        .mird-equations {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            padding: 2rem;
            border-radius: var(--border-radius);
            margin-top: 2rem;
            border-left: 4px solid var(--accent-color);
        }

        .mird-equations h4 {
            color: var(--accent-color);
            margin-bottom: 1rem;
            text-align: center;
        }

        .equation-explanation {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-style: italic;
            margin-top: 0.25rem;
            text-align: center;
        }

        /* Speed Display */
        #speedDisplay {
            font-weight: 700;
            color: var(--primary-color);
            font-family: var(--font-mono);
            margin-left: 0.5rem;
        }

        /* Analysis Content */
        .analysis-content {
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-radius: var(--border-radius);
            border-left: 4px solid var(--success-color);
        }

        .analysis-content h5 {
            color: var(--success-color);
            margin-bottom: 1rem;
        }

        #analysisText {
            line-height: 1.6;
            color: var(--text-secondary);
        }

        /* Advanced Animations and Effects */

        /* Pulse Animation for Active Elements */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        /* Glow Effect for Interactive Elements */
        .glow-effect {
            box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 20px rgba(37, 99, 235, 0.3); }
            to { box-shadow: 0 0 30px rgba(37, 99, 235, 0.6); }
        }

        /* Floating Animation for Particles */
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-10px) rotate(120deg); }
            66% { transform: translateY(5px) rotate(240deg); }
        }

        .floating-element {
            animation: float 6s ease-in-out infinite;
        }

        /* Slide In Animations */
        @keyframes slideInLeft {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideInUp {
            from { transform: translateY(100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes slideInDown {
            from { transform: translateY(-100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        /* Bounce Animation */
        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
            40%, 43% { transform: translate3d(0,-30px,0); }
            70% { transform: translate3d(0,-15px,0); }
            90% { transform: translate3d(0,-4px,0); }
        }

        .bounce-animation {
            animation: bounce 1s ease;
        }

        /* Shake Animation for Errors */
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
            20%, 40%, 60%, 80% { transform: translateX(10px); }
        }

        .shake-animation {
            animation: shake 0.5s ease-in-out;
        }

        /* Progress Bar Animations */
        .progress-bar-animated {
            background: linear-gradient(90deg,
                var(--primary-color) 0%,
                var(--secondary-color) 50%,
                var(--accent-color) 100%);
            background-size: 200% 100%;
            animation: progressGradient 2s ease infinite;
        }

        @keyframes progressGradient {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Loading Spinner */
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Notification System */
        .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            gap: 10px;
            max-width: 400px;
        }

        .notification {
            background: white;
            border-radius: var(--border-radius);
            padding: 1rem 1.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            border-left: 4px solid var(--primary-color);
            animation: slideInRight 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .notification.success {
            border-left-color: var(--success-color);
        }

        .notification.warning {
            border-left-color: var(--warning-color);
        }

        .notification.error {
            border-left-color: var(--error-color);
        }

        .notification-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .notification-message {
            color: var(--text-secondary);
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .notification-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .notification-action {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 0.25rem;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .notification-action.primary {
            background: var(--primary-color);
            color: white;
        }

        .notification-action.secondary {
            background: var(--light-color);
            color: var(--text-primary);
        }

        /* Search Functionality */
        .search-container {
            position: relative;
            margin-bottom: 2rem;
        }

        .search-input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid var(--border-color);
            border-radius: 2rem;
            font-size: 1rem;
            background: white;
            transition: var(--transition);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 1.2rem;
        }

        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-heavy);
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .search-result-item {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: var(--transition);
        }

        .search-result-item:hover {
            background: var(--light-color);
        }

        .search-result-item:last-child {
            border-bottom: none;
        }

        .search-result-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }

        .search-result-snippet {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        /* Theme Customization */
        .theme-selector {
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 1000;
        }

        .theme-button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .theme-button:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        .theme-options {
            position: absolute;
            bottom: 60px;
            left: 0;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-heavy);
            padding: 1rem;
            display: none;
            min-width: 200px;
        }

        .theme-option {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            cursor: pointer;
            border-radius: 0.25rem;
            transition: var(--transition);
        }

        .theme-option:hover {
            background: var(--light-color);
        }

        .theme-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Dark Theme Support */
        [data-theme="dark"] {
            --primary-color: #60a5fa;
            --secondary-color: #a78bfa;
            --accent-color: #34d399;
            --success-color: #10b981;
            --warning-color: #fbbf24;
            --error-color: #f87171;
            --dark-color: #f8fafc;
            --light-color: #1f2937;
            --text-primary: #f8fafc;
            --text-secondary: #d1d5db;
            --border-color: #374151;
        }

        [data-theme="dark"] body {
            background: linear-gradient(135deg, #1f2937 0%, #111827 50%, #1f2937 100%);
            color: var(--text-primary);
        }

        [data-theme="dark"] .book-container,
        [data-theme="dark"] .modal-content {
            background: rgba(31, 41, 55, 0.95);
            color: var(--text-primary);
        }

        /* Responsive Design Enhancements */
        @media (max-width: 1024px) {
            .decay-simulator-modal .modal-content,
            .dose-calculator-modal .modal-content {
                width: 95vw;
                max-width: none;
            }

            .real-time-data {
                grid-template-columns: 1fr;
            }

            .data-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .stats-grid,
            .dose-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .simulator-controls,
            .calculator-inputs {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .simulation-buttons {
                flex-direction: column;
                align-items: stretch;
            }

            .simulator-tabs,
            .calculator-tabs {
                flex-direction: column;
            }

            .tab-button {
                border-radius: 0;
                border-bottom: 1px solid var(--border-color);
                border-left: 3px solid transparent;
            }

            .tab-button.active {
                border-left-color: var(--primary-color);
                border-bottom-color: var(--primary-color);
            }

            .notification-container {
                left: 10px;
                right: 10px;
                max-width: none;
            }

            .theme-selector {
                bottom: 10px;
                left: 10px;
            }

            .data-table {
                font-size: 0.8rem;
            }

            .data-table th,
            .data-table td {
                padding: 0.5rem;
            }
        }

        @media (max-width: 480px) {
            .simulator-content,
            .calculator-content {
                padding: 1rem;
            }

            .chart-container {
                padding: 0.5rem;
            }

            .data-panel,
            .equation-panel {
                padding: 1rem;
            }

            .btn-primary,
            .btn-secondary,
            .btn-success,
            .btn-interactive {
                padding: 0.5rem 1rem;
                font-size: 0.9rem;
            }

            .data-item {
                padding: 0.5rem;
            }

            .stat-item,
            .dose-item {
                padding: 1rem;
            }
        }

        /* Print Styles */
        @media print {
            .modal,
            .floating-atoms,
            .theme-selector,
            .notification-container {
                display: none !important;
            }

            .book-container {
                box-shadow: none;
                border: 1px solid #ccc;
            }

            .chapter-card,
            .presentation-card,
            .tool-card {
                break-inside: avoid;
                box-shadow: none;
                border: 1px solid #ccc;
            }
        }

        /* High Contrast Mode */
        @media (prefers-contrast: high) {
            :root {
                --primary-color: #000080;
                --secondary-color: #800080;
                --accent-color: #008080;
                --text-primary: #000000;
                --text-secondary: #333333;
                --border-color: #000000;
            }

            .btn-primary,
            .btn-secondary,
            .btn-success,
            .btn-interactive {
                border: 2px solid #000000;
            }
        }

        /* Slide Out Animations */
        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        @keyframes slideOutLeft {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(-100%); opacity: 0; }
        }

        @keyframes slideOutUp {
            from { transform: translateY(0); opacity: 1; }
            to { transform: translateY(-100%); opacity: 0; }
        }

        @keyframes slideOutDown {
            from { transform: translateY(0); opacity: 1; }
            to { transform: translateY(100%); opacity: 0; }
        }

        /* Interactive Hover Effects */
        .interactive-hover {
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .interactive-hover::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .interactive-hover:hover::before {
            left: 100%;
        }

        /* Particle Effects */
        .particle-effect {
            position: relative;
            overflow: hidden;
        }

        .particle-effect::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.3) 2px, transparent 2px),
                radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.3) 2px, transparent 2px),
                radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.3) 2px, transparent 2px);
            background-size: 50px 50px, 60px 60px, 40px 40px;
            animation: particleFloat 20s linear infinite;
            pointer-events: none;
        }

        @keyframes particleFloat {
            0% { transform: translateY(0) rotate(0deg); }
            100% { transform: translateY(-100vh) rotate(360deg); }
        }

        /* Enhanced Button Effects */
        .btn-enhanced {
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .btn-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: width 0.3s ease;
            z-index: -1;
        }

        .btn-enhanced:hover::before {
            width: 100%;
        }

        /* Tooltip System */
        .tooltip {
            position: relative;
            cursor: help;
        }

        .tooltip::before {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            font-size: 0.8rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
            z-index: 1000;
        }

        .tooltip::after {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
        }

        .tooltip:hover::before,
        .tooltip:hover::after {
            opacity: 1;
            visibility: visible;
        }

        /* Loading States */
        .loading-state {
            position: relative;
            pointer-events: none;
        }

        .loading-state::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid var(--border-color);
            border-top-color: var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* Success/Error States */
        .success-state {
            border-color: var(--success-color) !important;
            background: rgba(16, 185, 129, 0.1) !important;
        }

        .error-state {
            border-color: var(--error-color) !important;
            background: rgba(239, 68, 68, 0.1) !important;
        }

        /* Interactive Focus States */
        .focus-visible {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* Scroll Animations */
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease, transform 0.6s ease;
        }

        .scroll-reveal.revealed {
            opacity: 1;
            transform: translateY(0);
        }

        /* Enhanced Card Interactions */
        .card-3d {
            transform-style: preserve-3d;
            transition: transform 0.3s ease;
        }

        .card-3d:hover {
            transform: rotateY(5deg) rotateX(5deg);
        }

        /* Gradient Text Effects */
        .gradient-text {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 3s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Morphing Shapes */
        .morphing-shape {
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
            animation: morph 8s ease-in-out infinite;
        }

        @keyframes morph {
            0% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
            25% { border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%; }
            50% { border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%; }
            75% { border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%; }
            100% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
        }

        /* Reduced Motion Support */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }

            .floating-element,
            .pulse-animation,
            .glow-effect,
            .morphing-shape,
            .gradient-text {
                animation: none;
            }

            .scroll-reveal {
                opacity: 1;
                transform: none;
            }
        }

        /* Enhanced Accessibility */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Skip Links */
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: var(--primary-color);
            color: white;
            padding: 8px;
            text-decoration: none;
            border-radius: 0.25rem;
            z-index: 10000;
            transition: top 0.3s ease;
        }

        .skip-link:focus {
            top: 6px;
        }

        /* Performance Optimizations */
        .will-change-transform {
            will-change: transform;
        }

        .will-change-opacity {
            will-change: opacity;
        }

        .gpu-accelerated {
            transform: translateZ(0);
            backface-visibility: hidden;
            perspective: 1000px;
        }

        /* 3D Visualization Styles */
        .atomic-3d-modal .modal-content,
        .camera-3d-modal .modal-content {
            max-width: 95vw;
            width: 1400px;
            max-height: 95vh;
        }

        .visualization-content {
            padding: 2rem;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: white;
        }

        .visualization-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
        }

        .visualization-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .threejs-container {
            background: #000;
            border-radius: var(--border-radius);
            border: 2px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            position: relative;
            min-height: 600px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .threejs-container canvas {
            border-radius: var(--border-radius);
        }

        .visualization-info,
        .camera-stats {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
        }

        .visualization-info h4,
        .camera-stats h4 {
            color: #4fc3f7;
            margin-bottom: 1rem;
            text-align: center;
            font-size: 1.2rem;
        }

        .property-grid,
        .stats-grid {
            display: grid;
            gap: 1rem;
        }

        .property-item,
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 0.5rem;
            border-left: 3px solid #4fc3f7;
        }

        .property-label,
        .stat-label {
            font-size: 0.9rem;
            color: #b0bec5;
        }

        .property-value,
        .stat-value {
            font-weight: 700;
            color: #4fc3f7;
            font-family: var(--font-mono);
        }

        .educational-content {
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
        }

        .educational-content h4 {
            color: #81c784;
            margin-bottom: 1rem;
            text-align: center;
        }

        .component-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .component-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 1rem;
            border-radius: 0.5rem;
            border-left: 3px solid #81c784;
        }

        .component-item h5 {
            color: #81c784;
            margin-bottom: 0.5rem;
        }

        .component-item p {
            color: #e0e0e0;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        /* 3D Control Styling */
        .visualization-controls .control-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .visualization-controls label {
            color: #4fc3f7;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .visualization-controls input,
        .visualization-controls select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.5rem;
            border-radius: 0.25rem;
        }

        .visualization-controls input:focus,
        .visualization-controls select:focus {
            outline: none;
            border-color: #4fc3f7;
            box-shadow: 0 0 0 2px rgba(79, 195, 247, 0.3);
        }

        #speedValue,
        #activityValue {
            color: #4fc3f7;
            font-weight: 700;
            font-family: var(--font-mono);
        }

        /* Loading indicator for 3D scenes */
        .threejs-container::before {
            content: 'Loading 3D Visualization...';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #4fc3f7;
            font-size: 1.2rem;
            z-index: 1;
        }

        .threejs-container.loaded::before {
            display: none;
        }

        /* Responsive 3D Visualization */
        @media (max-width: 1024px) {
            .visualization-container {
                grid-template-columns: 1fr;
            }

            .threejs-container {
                min-height: 400px;
            }

            .component-info {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .visualization-controls {
                grid-template-columns: 1fr;
            }

            .visualization-content {
                padding: 1rem;
            }

            .threejs-container {
                min-height: 300px;
            }
        }

        .table-of-contents {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .toc-title {
            font-size: 1.8em;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .part {
            margin-bottom: 25px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            transition: transform 0.3s ease;
        }

        .part:hover {
            transform: translateY(-5px);
        }

        .part-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #fff;
        }

        .chapter {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .chapter:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(10px);
        }

        .chapter-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .chapter-content {
            font-size: 0.95em;
            opacity: 0.9;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            transform: translateY(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-10px);
        }

        .feature-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .feature-title {
            font-size: 1.3em;
            margin-bottom: 10px;
        }

        .feature-description {
            font-size: 0.95em;
            opacity: 0.9;
        }

        .interactive-section {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: center;
        }

        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            position: relative;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #333;
        }

        .qa-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
        }

        .question {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .answer {
            color: #34495e;
            padding: 10px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
        }

        .floating-atoms {
            position: fixed;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .atom {
            position: absolute;
            width: 20px;
            height: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s infinite ease-in-out;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 2em;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="floating-atoms"></div>
    
    <div class="container">
        <header class="animate__animated animate__fadeInDown">
            <h1>Nuclear Medicine Physics & Biomedical Engineering</h1>
            <div class="author">Dr. Mohammed Yagoub Esmail</div>
            <div class="author-title">Associate Professor of Biomedical Engineering</div>
            <div class="author-title">Vice Dean, College of Engineering</div>
            <div class="author-title">Sudan University of Science and Technology (SUST)</div>

            <div class="copyright">
                <strong>© 2025 Dr. Mohammed Yagoub Esmail</strong><br>
                Email: <EMAIL><br>
                Phone: +249912867327, +966538076790<br>
                All rights reserved. No part of this publication may be reproduced without permission.
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <div class="description">
                A comprehensive academic resource combining Nuclear Medicine Physics with Biomedical Engineering principles.
                This interactive educational module features detailed theoretical content, practical applications,
                animated presentations, and step-by-step learning modules designed for advanced undergraduate and graduate students.
                The content integrates cutting-edge research with clinical applications, providing a thorough understanding
                of nuclear medicine technologies and their biomedical engineering foundations.
            </div>
        </header>

        <!-- Navigation Menu -->
        <nav class="navigation animate__animated animate__fadeInUp">
            <button class="nav-toggle" id="navToggle">
                <i class="fas fa-bars"></i>
            </button>
            <div class="nav-menu" id="navMenu">
                <div class="nav-item active" data-section="overview">
                    <i class="fas fa-home"></i> Overview
                </div>
                <div class="nav-item" data-section="chapters">
                    <i class="fas fa-book"></i> Chapters
                </div>
                <div class="nav-item" data-section="presentations">
                    <i class="fas fa-presentation"></i> Presentations
                </div>
                <div class="nav-item" data-section="interactive">
                    <i class="fas fa-atom"></i> Interactive Tools
                </div>
                <div class="nav-item" data-section="references">
                    <i class="fas fa-citation"></i> References
                </div>
                <div class="nav-item" data-section="resources">
                    <i class="fas fa-download"></i> Resources
                </div>
            </div>
        </nav>

        <!-- Main Content Sections -->
        <div class="content-sections">
            <!-- Overview Section -->
            <section id="overview" class="content-section active">
                <div class="book-container animate__animated animate__fadeIn">
                    <h2><i class="fas fa-atom"></i> Course Overview</h2>
                    <div class="overview-grid">
                        <div class="overview-card">
                            <h3><i class="fas fa-graduation-cap"></i> Learning Objectives</h3>
                            <ul>
                                <li>Understand fundamental principles of nuclear medicine physics</li>
                                <li>Master biomedical engineering applications in nuclear medicine</li>
                                <li>Analyze radiation detection and imaging systems</li>
                                <li>Evaluate safety protocols and radiation protection</li>
                                <li>Apply quality control procedures in clinical settings</li>
                            </ul>
                        </div>
                        <div class="overview-card">
                            <h3><i class="fas fa-users"></i> Target Audience</h3>
                            <ul>
                                <li>Graduate students in Biomedical Engineering</li>
                                <li>Medical Physics residents and professionals</li>
                                <li>Nuclear medicine technologists</li>
                                <li>Healthcare professionals in imaging</li>
                                <li>Research scientists in medical imaging</li>
                            </ul>
                        </div>
                        <div class="overview-card">
                            <h3><i class="fas fa-clock"></i> Course Duration</h3>
                            <ul>
                                <li>Total Duration: 16 weeks (1 semester)</li>
                                <li>Lecture Hours: 48 hours</li>
                                <li>Laboratory Sessions: 32 hours</li>
                                <li>Independent Study: 80 hours</li>
                                <li>Assessment: Continuous + Final Exam</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Chapters Section -->
            <section id="chapters" class="content-section">
                <div class="book-container">
                    <h2><i class="fas fa-book-open"></i> Comprehensive Chapter Outline</h2>

                    <!-- Part I: Fundamental Physics -->
                    <div class="part-container">
                        <div class="part-header">
                            <h3><i class="fas fa-atom"></i> Part I: Nuclear Physics Fundamentals</h3>
                            <p>Foundation concepts in atomic and nuclear physics essential for nuclear medicine</p>
                        </div>

                        <div class="chapter-grid">
                            <div class="chapter-card" data-chapter="1">
                                <div class="chapter-number">01</div>
                                <div class="chapter-content">
                                    <h4>Atomic Structure and Nuclear Properties</h4>
                                    <p class="chapter-description">
                                        Comprehensive study of atomic structure, nuclear composition, and fundamental properties.
                                        <strong>Key Topics:</strong> Atomic models, nuclear binding energy, mass-energy equivalence.
                                    </p>
                                    <div class="chapter-features">
                                        <span class="feature-tag"><i class="fas fa-chart-line"></i> 15 Figures</span>
                                        <span class="feature-tag"><i class="fas fa-table"></i> 8 Tables</span>
                                        <span class="feature-tag"><i class="fas fa-calculator"></i> 25 Equations</span>
                                        <span class="feature-tag"><i class="fas fa-quote-right"></i> 45 Citations</span>
                                    </div>
                                    <div class="learning-outcomes">
                                        <h5>Learning Outcomes:</h5>
                                        <ul>
                                            <li>Analyze atomic structure using quantum mechanical principles (Schrödinger, 1926)</li>
                                            <li>Calculate nuclear binding energies and stability (Weizsäcker, 1935)</li>
                                            <li>Evaluate nuclear properties relevant to medical applications</li>
                                        </ul>
                                    </div>
                                    <div class="key-equations">
                                        <h5>Key Equations:</h5>
                                        <div class="equation">E = mc² (Mass-Energy Equivalence)</div>
                                        <div class="equation">BE = [Z·m_H + N·m_n - M]c² (Binding Energy)</div>
                                    </div>
                                    <div class="references">
                                        <h5>Key References:</h5>
                                        <ul>
                                            <li>Segre, E. (1977). Nuclei and Particles. Benjamin/Cummings</li>
                                            <li>Evans, R.D. (1955). The Atomic Nucleus. McGraw-Hill</li>
                                            <li>Turner, J.E. (2007). Atoms, Radiation, and Radiation Protection. Wiley</li>
                                        </ul>
                                    </div>
                                    <div class="chapter-actions">
                                        <a href="pages/nuclear_medicine_physics_chapters.html#chapter1" class="chapter-link">
                                            <i class="fas fa-book-open"></i> Detailed Chapter
                                        </a>
                                        <a href="#" class="chapter-link secondary">
                                            <i class="fas fa-download"></i> Download PDF
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="chapter-card" data-chapter="2">
                                <div class="chapter-number">02</div>
                                <div class="chapter-content">
                                    <h4>Radioactive Decay and Nuclear Transformations</h4>
                                    <p class="chapter-description">
                                        Detailed analysis of radioactive decay processes, decay laws, and nuclear transformations.
                                        <strong>Key Topics:</strong> Decay modes, half-life calculations, secular equilibrium (Bateman, 1910).
                                    </p>
                                    <div class="chapter-features">
                                        <span class="feature-tag"><i class="fas fa-chart-line"></i> 22 Figures</span>
                                        <span class="feature-tag"><i class="fas fa-table"></i> 12 Tables</span>
                                        <span class="feature-tag"><i class="fas fa-calculator"></i> 35 Equations</span>
                                        <span class="feature-tag"><i class="fas fa-quote-right"></i> 52 Citations</span>
                                    </div>
                                    <div class="learning-outcomes">
                                        <h5>Learning Outcomes:</h5>
                                        <ul>
                                            <li>Apply radioactive decay law and calculate activity (Rutherford & Soddy, 1903)</li>
                                            <li>Analyze branching ratios and decay schemes</li>
                                            <li>Evaluate secular and transient equilibrium conditions</li>
                                        </ul>
                                    </div>
                                    <div class="key-equations">
                                        <h5>Key Equations:</h5>
                                        <div class="equation">N(t) = N₀e^(-λt) (Decay Law)</div>
                                        <div class="equation">A(t) = λN(t) = A₀e^(-λt) (Activity)</div>
                                        <div class="equation">t₁/₂ = ln(2)/λ (Half-life)</div>
                                    </div>
                                </div>
                            </div>

                            <div class="chapter-card" data-chapter="3">
                                <div class="chapter-number">03</div>
                                <div class="chapter-content">
                                    <h4>Radiation Interaction with Matter</h4>
                                    <p class="chapter-description">
                                        Comprehensive study of radiation-matter interactions including photon and particle interactions.
                                        <strong>Key Topics:</strong> Photoelectric effect, Compton scattering, pair production (Klein & Nishina, 1929).
                                    </p>
                                    <div class="chapter-features">
                                        <span class="feature-tag"><i class="fas fa-chart-line"></i> 28 Figures</span>
                                        <span class="feature-tag"><i class="fas fa-table"></i> 15 Tables</span>
                                        <span class="feature-tag"><i class="fas fa-calculator"></i> 42 Equations</span>
                                        <span class="feature-tag"><i class="fas fa-quote-right"></i> 68 Citations</span>
                                    </div>
                                    <div class="learning-outcomes">
                                        <h5>Learning Outcomes:</h5>
                                        <ul>
                                            <li>Calculate cross-sections for photon interactions (Klein & Nishina, 1929)</li>
                                            <li>Analyze energy transfer and absorption coefficients</li>
                                            <li>Evaluate charged particle energy loss mechanisms (Bethe-Bloch, 1930)</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Part II: Instrumentation and Detection -->
                    <div class="part-container">
                        <div class="part-header">
                            <h3><i class="fas fa-microscope"></i> Part II: Radiation Detection and Instrumentation</h3>
                            <p>Advanced study of radiation detection principles and nuclear medicine instrumentation</p>
                        </div>

                        <div class="chapter-grid">
                            <div class="chapter-card" data-chapter="4">
                                <div class="chapter-number">04</div>
                                <div class="chapter-content">
                                    <h4>Radiation Detection Principles</h4>
                                    <p class="chapter-description">
                                        Fundamental principles of radiation detection including gas-filled detectors, scintillation detectors, and semiconductor detectors.
                                        <strong>Key Topics:</strong> Detector physics, signal formation, noise analysis (Knoll, 2010).
                                    </p>
                                    <div class="chapter-features">
                                        <span class="feature-tag"><i class="fas fa-chart-line"></i> 32 Figures</span>
                                        <span class="feature-tag"><i class="fas fa-table"></i> 18 Tables</span>
                                        <span class="feature-tag"><i class="fas fa-calculator"></i> 28 Equations</span>
                                        <span class="feature-tag"><i class="fas fa-quote-right"></i> 75 Citations</span>
                                    </div>
                                    <div class="learning-outcomes">
                                        <h5>Learning Outcomes:</h5>
                                        <ul>
                                            <li>Analyze detector response functions and energy resolution</li>
                                            <li>Calculate detection efficiency and dead time corrections</li>
                                            <li>Evaluate noise sources and signal-to-noise ratios</li>
                                        </ul>
                                    </div>
                                    <div class="key-equations">
                                        <h5>Key Equations:</h5>
                                        <div class="equation">R = FWHM/E₀ (Energy Resolution)</div>
                                        <div class="equation">ε = N_detected/N_emitted (Detection Efficiency)</div>
                                    </div>
                                </div>
                            </div>

                            <div class="chapter-card" data-chapter="5">
                                <div class="chapter-number">05</div>
                                <div class="chapter-content">
                                    <h4>Gamma Camera Systems</h4>
                                    <p class="chapter-description">
                                        Comprehensive analysis of gamma camera design, collimation, and image formation.
                                        <strong>Key Topics:</strong> Anger camera principles, collimator design, spatial resolution (Anger, 1958).
                                    </p>
                                    <div class="chapter-features">
                                        <span class="feature-tag"><i class="fas fa-chart-line"></i> 45 Figures</span>
                                        <span class="feature-tag"><i class="fas fa-table"></i> 22 Tables</span>
                                        <span class="feature-tag"><i class="fas fa-calculator"></i> 38 Equations</span>
                                        <span class="feature-tag"><i class="fas fa-quote-right"></i> 89 Citations</span>
                                    </div>
                                    <div class="learning-outcomes">
                                        <h5>Learning Outcomes:</h5>
                                        <ul>
                                            <li>Design optimal collimator systems for specific applications</li>
                                            <li>Calculate spatial and energy resolution parameters</li>
                                            <li>Analyze image quality metrics and performance characteristics</li>
                                        </ul>
                                    </div>
                                    <div class="key-equations">
                                        <h5>Key Equations:</h5>
                                        <div class="equation">R_c = d(l_e + b)/l_e (Collimator Resolution)</div>
                                        <div class="equation">S = K²t²/l_e (Sensitivity)</div>
                                    </div>
                                </div>
                            </div>

                            <div class="chapter-card" data-chapter="6">
                                <div class="chapter-number">06</div>
                                <div class="chapter-content">
                                    <h4>SPECT and PET Imaging Systems</h4>
                                    <p class="chapter-description">
                                        Advanced tomographic imaging principles including SPECT and PET system design and reconstruction algorithms.
                                        <strong>Key Topics:</strong> Tomographic reconstruction, attenuation correction, scatter correction (Shepp & Logan, 1974).
                                    </p>
                                    <div class="chapter-features">
                                        <span class="feature-tag"><i class="fas fa-chart-line"></i> 38 Figures</span>
                                        <span class="feature-tag"><i class="fas fa-table"></i> 25 Tables</span>
                                        <span class="feature-tag"><i class="fas fa-calculator"></i> 45 Equations</span>
                                        <span class="feature-tag"><i class="fas fa-quote-right"></i> 95 Citations</span>
                                    </div>
                                    <div class="learning-outcomes">
                                        <h5>Learning Outcomes:</h5>
                                        <ul>
                                            <li>Implement filtered back-projection reconstruction algorithms</li>
                                            <li>Apply attenuation and scatter correction methods</li>
                                            <li>Evaluate image quality in tomographic systems</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Part III: Biomedical Engineering Applications -->
                    <div class="part-container">
                        <div class="part-header">
                            <h3><i class="fas fa-heartbeat"></i> Part III: Biomedical Engineering Applications</h3>
                            <p>Integration of nuclear medicine with biomedical engineering principles and clinical applications</p>
                        </div>

                        <div class="chapter-grid">
                            <div class="chapter-card" data-chapter="7">
                                <div class="chapter-number">07</div>
                                <div class="chapter-content">
                                    <h4>Radiopharmaceuticals and Tracer Kinetics</h4>
                                    <p class="chapter-description">
                                        Comprehensive study of radiopharmaceutical design, synthesis, and kinetic modeling.
                                        <strong>Key Topics:</strong> Tracer principles, compartmental modeling, pharmacokinetics (Phelps et al., 1979).
                                    </p>
                                    <div class="chapter-features">
                                        <span class="feature-tag"><i class="fas fa-chart-line"></i> 35 Figures</span>
                                        <span class="feature-tag"><i class="fas fa-table"></i> 20 Tables</span>
                                        <span class="feature-tag"><i class="fas fa-calculator"></i> 32 Equations</span>
                                        <span class="feature-tag"><i class="fas fa-quote-right"></i> 78 Citations</span>
                                    </div>
                                    <div class="learning-outcomes">
                                        <h5>Learning Outcomes:</h5>
                                        <ul>
                                            <li>Design compartmental models for tracer kinetics</li>
                                            <li>Calculate pharmacokinetic parameters and clearance rates</li>
                                            <li>Evaluate biodistribution and dosimetry calculations</li>
                                        </ul>
                                    </div>
                                    <div class="key-equations">
                                        <h5>Key Equations:</h5>
                                        <div class="equation">dC/dt = K₁C_p - k₂C_t (Two-compartment model)</div>
                                        <div class="equation">SUV = C_tissue/(Dose/Weight) (Standardized Uptake Value)</div>
                                    </div>
                                </div>
                            </div>

                            <div class="chapter-card" data-chapter="8">
                                <div class="chapter-number">08</div>
                                <div class="chapter-content">
                                    <h4>Image Processing and Analysis</h4>
                                    <p class="chapter-description">
                                        Advanced image processing techniques for nuclear medicine including filtering, segmentation, and quantitative analysis.
                                        <strong>Key Topics:</strong> Digital filtering, ROI analysis, statistical parametric mapping (Friston et al., 1995).
                                    </p>
                                    <div class="chapter-features">
                                        <span class="feature-tag"><i class="fas fa-chart-line"></i> 42 Figures</span>
                                        <span class="feature-tag"><i class="fas fa-table"></i> 15 Tables</span>
                                        <span class="feature-tag"><i class="fas fa-calculator"></i> 28 Equations</span>
                                        <span class="feature-tag"><i class="fas fa-quote-right"></i> 65 Citations</span>
                                    </div>
                                    <div class="learning-outcomes">
                                        <h5>Learning Outcomes:</h5>
                                        <ul>
                                            <li>Implement digital filtering and enhancement algorithms</li>
                                            <li>Perform quantitative ROI and VOI analysis</li>
                                            <li>Apply statistical methods for image comparison</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="chapter-card" data-chapter="9">
                                <div class="chapter-number">09</div>
                                <div class="chapter-content">
                                    <h4>Radiation Dosimetry and Safety</h4>
                                    <p class="chapter-description">
                                        Comprehensive radiation dosimetry calculations and safety protocols in nuclear medicine.
                                        <strong>Key Topics:</strong> MIRD methodology, organ dosimetry, radiation protection (Loevinger et al., 1988).
                                    </p>
                                    <div class="chapter-features">
                                        <span class="feature-tag"><i class="fas fa-chart-line"></i> 25 Figures</span>
                                        <span class="feature-tag"><i class="fas fa-table"></i> 30 Tables</span>
                                        <span class="feature-tag"><i class="fas fa-calculator"></i> 40 Equations</span>
                                        <span class="feature-tag"><i class="fas fa-quote-right"></i> 85 Citations</span>
                                    </div>
                                    <div class="learning-outcomes">
                                        <h5>Learning Outcomes:</h5>
                                        <ul>
                                            <li>Calculate organ and effective dose using MIRD methodology</li>
                                            <li>Implement radiation safety protocols and ALARA principles</li>
                                            <li>Evaluate regulatory compliance and quality assurance</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Part IV: Advanced Topics and Clinical Applications -->
                    <div class="part-container">
                        <div class="part-header">
                            <h3><i class="fas fa-brain"></i> Part IV: Advanced Topics and Clinical Applications</h3>
                            <p>Cutting-edge developments and specialized applications in nuclear medicine</p>
                        </div>

                        <div class="chapter-grid">
                            <div class="chapter-card" data-chapter="10">
                                <div class="chapter-number">10</div>
                                <div class="chapter-content">
                                    <h4>Molecular Imaging and Theranostics</h4>
                                    <p class="chapter-description">
                                        Advanced molecular imaging techniques and theranostic applications combining diagnosis and therapy.
                                        <strong>Key Topics:</strong> Molecular probes, targeted therapy, personalized medicine (Weissleder & Mahmood, 2001).
                                    </p>
                                    <div class="chapter-features">
                                        <span class="feature-tag"><i class="fas fa-chart-line"></i> 40 Figures</span>
                                        <span class="feature-tag"><i class="fas fa-table"></i> 18 Tables</span>
                                        <span class="feature-tag"><i class="fas fa-calculator"></i> 25 Equations</span>
                                        <span class="feature-tag"><i class="fas fa-quote-right"></i> 92 Citations</span>
                                    </div>
                                    <div class="learning-outcomes">
                                        <h5>Learning Outcomes:</h5>
                                        <ul>
                                            <li>Design molecular imaging probes for specific targets</li>
                                            <li>Evaluate theranostic approaches and treatment planning</li>
                                            <li>Analyze molecular imaging data and biomarker validation</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="chapter-card" data-chapter="11">
                                <div class="chapter-number">11</div>
                                <div class="chapter-content">
                                    <h4>Artificial Intelligence in Nuclear Medicine</h4>
                                    <p class="chapter-description">
                                        Integration of AI and machine learning techniques in nuclear medicine imaging and analysis.
                                        <strong>Key Topics:</strong> Deep learning, image reconstruction, automated diagnosis (LeCun et al., 2015).
                                    </p>
                                    <div class="chapter-features">
                                        <span class="feature-tag"><i class="fas fa-chart-line"></i> 35 Figures</span>
                                        <span class="feature-tag"><i class="fas fa-table"></i> 12 Tables</span>
                                        <span class="feature-tag"><i class="fas fa-calculator"></i> 20 Equations</span>
                                        <span class="feature-tag"><i class="fas fa-quote-right"></i> 88 Citations</span>
                                    </div>
                                    <div class="learning-outcomes">
                                        <h5>Learning Outcomes:</h5>
                                        <ul>
                                            <li>Implement machine learning algorithms for image analysis</li>
                                            <li>Develop AI-based diagnostic tools and decision support systems</li>
                                            <li>Evaluate performance metrics and validation strategies</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="chapter-card" data-chapter="12">
                                <div class="chapter-number">12</div>
                                <div class="chapter-content">
                                    <h4>Quality Control and Regulatory Compliance</h4>
                                    <p class="chapter-description">
                                        Comprehensive quality control procedures and regulatory requirements for nuclear medicine facilities.
                                        <strong>Key Topics:</strong> QC protocols, accreditation standards, regulatory compliance (NEMA, IEC standards).
                                    </p>
                                    <div class="chapter-features">
                                        <span class="feature-tag"><i class="fas fa-chart-line"></i> 30 Figures</span>
                                        <span class="feature-tag"><i class="fas fa-table"></i> 25 Tables</span>
                                        <span class="feature-tag"><i class="fas fa-calculator"></i> 15 Equations</span>
                                        <span class="feature-tag"><i class="fas fa-quote-right"></i> 55 Citations</span>
                                    </div>
                                    <div class="learning-outcomes">
                                        <h5>Learning Outcomes:</h5>
                                        <ul>
                                            <li>Implement comprehensive QC programs for nuclear medicine</li>
                                            <li>Evaluate compliance with national and international standards</li>
                                            <li>Design quality assurance protocols and documentation systems</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Presentations Section -->
            <section id="presentations" class="content-section">
                <div class="book-container">
                    <h2><i class="fas fa-presentation"></i> Interactive Slide Presentations</h2>
                    <p class="section-description">
                        Step-by-step animated presentations designed for effective teaching and learning.
                        Each presentation includes interactive elements, animations, and comprehensive visual aids.
                    </p>

                    <div class="presentations-grid">
                        <div class="presentation-card" data-presentation="nuclear-physics">
                            <div class="presentation-thumbnail">
                                <i class="fas fa-atom presentation-icon"></i>
                                <div class="play-button">
                                    <i class="fas fa-play"></i>
                                </div>
                            </div>
                            <div class="presentation-content">
                                <h3>Nuclear Physics Fundamentals</h3>
                                <p>Interactive presentation covering atomic structure, nuclear properties, and radioactive decay.</p>
                                <div class="presentation-stats">
                                    <span><i class="fas fa-slides"></i> 45 Slides</span>
                                    <span><i class="fas fa-clock"></i> 60 min</span>
                                    <span><i class="fas fa-chart-bar"></i> 15 Animations</span>
                                </div>
                                <div class="presentation-topics">
                                    <span class="topic-tag">Atomic Models</span>
                                    <span class="topic-tag">Nuclear Binding</span>
                                    <span class="topic-tag">Decay Processes</span>
                                </div>
                            </div>
                        </div>

                        <div class="presentation-card" data-presentation="detection-systems">
                            <div class="presentation-thumbnail">
                                <i class="fas fa-microscope presentation-icon"></i>
                                <div class="play-button">
                                    <i class="fas fa-play"></i>
                                </div>
                            </div>
                            <div class="presentation-content">
                                <h3>Radiation Detection Systems</h3>
                                <p>Comprehensive overview of detection principles, gamma cameras, and imaging systems.</p>
                                <div class="presentation-stats">
                                    <span><i class="fas fa-slides"></i> 52 Slides</span>
                                    <span><i class="fas fa-clock"></i> 75 min</span>
                                    <span><i class="fas fa-chart-bar"></i> 20 Animations</span>
                                </div>
                                <div class="presentation-topics">
                                    <span class="topic-tag">Detector Physics</span>
                                    <span class="topic-tag">Gamma Cameras</span>
                                    <span class="topic-tag">Image Formation</span>
                                </div>
                            </div>
                        </div>

                        <div class="presentation-card" data-presentation="spect-pet">
                            <div class="presentation-thumbnail">
                                <i class="fas fa-brain presentation-icon"></i>
                                <div class="play-button">
                                    <i class="fas fa-play"></i>
                                </div>
                            </div>
                            <div class="presentation-content">
                                <h3>SPECT and PET Imaging</h3>
                                <p>Advanced tomographic imaging techniques with reconstruction algorithms and clinical applications.</p>
                                <div class="presentation-stats">
                                    <span><i class="fas fa-slides"></i> 68 Slides</span>
                                    <span><i class="fas fa-clock"></i> 90 min</span>
                                    <span class="topic-tag">25 Animations</span>
                                </div>
                                <div class="presentation-topics">
                                    <span class="topic-tag">Tomography</span>
                                    <span class="topic-tag">Reconstruction</span>
                                    <span class="topic-tag">Clinical Apps</span>
                                </div>
                            </div>
                        </div>

                        <div class="presentation-card" data-presentation="biomedical-engineering">
                            <div class="presentation-thumbnail">
                                <i class="fas fa-heartbeat presentation-icon"></i>
                                <div class="play-button">
                                    <i class="fas fa-play"></i>
                                </div>
                            </div>
                            <div class="presentation-content">
                                <h3>Biomedical Engineering Applications</h3>
                                <p>Integration of engineering principles with nuclear medicine for advanced healthcare solutions.</p>
                                <div class="presentation-stats">
                                    <span><i class="fas fa-slides"></i> 55 Slides</span>
                                    <span><i class="fas fa-clock"></i> 80 min</span>
                                    <span><i class="fas fa-chart-bar"></i> 18 Animations</span>
                                </div>
                                <div class="presentation-topics">
                                    <span class="topic-tag">System Design</span>
                                    <span class="topic-tag">Signal Processing</span>
                                    <span class="topic-tag">Quality Control</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Interactive Tools Section -->
            <section id="interactive" class="content-section">
                <div class="book-container">
                    <h2><i class="fas fa-atom"></i> Interactive Learning Tools</h2>
                    <p class="section-description">
                        Hands-on interactive tools and simulations to enhance understanding of nuclear medicine physics concepts.
                    </p>

                    <div class="tools-grid">
                        <div class="tool-card" data-tool="decay-simulator">
                            <div class="tool-header">
                                <i class="fas fa-chart-line tool-icon"></i>
                                <h3>Radioactive Decay Simulator</h3>
                            </div>
                            <div class="tool-content">
                                <p>Interactive simulation of radioactive decay processes with real-time visualization.</p>
                                <div class="tool-features">
                                    <span class="feature">Real-time Calculations</span>
                                    <span class="feature">Multiple Isotopes</span>
                                    <span class="feature">Graphical Display</span>
                                </div>
                                <button type="button" class="tool-button" onclick="openTool('decay-simulator')">
                                    <i class="fas fa-play"></i> Launch Simulator
                                </button>
                            </div>
                        </div>

                        <div class="tool-card" data-tool="dose-calculator">
                            <div class="tool-header">
                                <i class="fas fa-calculator tool-icon"></i>
                                <h3>Radiation Dose Calculator</h3>
                            </div>
                            <div class="tool-content">
                                <p>Comprehensive dosimetry calculator using MIRD methodology for organ dose estimation.</p>
                                <div class="tool-features">
                                    <span class="feature">MIRD Methodology</span>
                                    <span class="feature">Organ Dosimetry</span>
                                    <span class="feature">Safety Assessment</span>
                                </div>
                                <button type="button" class="tool-button" onclick="openTool('dose-calculator')">
                                    <i class="fas fa-calculator"></i> Calculate Dose
                                </button>
                            </div>
                        </div>

                        <div class="tool-card" data-tool="image-reconstruction">
                            <div class="tool-header">
                                <i class="fas fa-image tool-icon"></i>
                                <h3>Image Reconstruction Demo</h3>
                            </div>
                            <div class="tool-content">
                                <p>Interactive demonstration of tomographic reconstruction algorithms and filtering techniques.</p>
                                <div class="tool-features">
                                    <span class="feature">FBP Algorithm</span>
                                    <span class="feature">Filter Selection</span>
                                    <span class="feature">3D Visualization</span>
                                </div>
                                <button type="button" class="tool-button" onclick="openTool('image-reconstruction')">
                                    <i class="fas fa-image"></i> Start Demo
                                </button>
                            </div>
                        </div>

                        <div class="tool-card" data-tool="detector-response">
                            <div class="tool-header">
                                <i class="fas fa-wave-square tool-icon"></i>
                                <h3>Detector Response Analyzer</h3>
                            </div>
                            <div class="tool-content">
                                <p>Analyze detector response functions, energy resolution, and efficiency characteristics.</p>
                                <div class="tool-features">
                                    <span class="feature">Energy Spectra</span>
                                    <span class="feature">Resolution Analysis</span>
                                    <span class="feature">Efficiency Curves</span>
                                </div>
                                <button type="button" class="tool-button" onclick="openTool('detector-response')">
                                    <i class="fas fa-wave-square"></i> Analyze Response
                                </button>
                            </div>
                        </div>

                        <div class="tool-card" data-tool="phantom-studies">
                            <div class="tool-header">
                                <i class="fas fa-user-md tool-icon"></i>
                                <h3>Virtual Phantom Studies</h3>
                            </div>
                            <div class="tool-content">
                                <p>Virtual phantom simulations for quality control and performance evaluation studies.</p>
                                <div class="tool-features">
                                    <span class="feature">QC Protocols</span>
                                    <span class="feature">Performance Metrics</span>
                                    <span class="feature">Report Generation</span>
                                </div>
                                <button type="button" class="tool-button" onclick="openTool('phantom-studies')">
                                    <i class="fas fa-user-md"></i> Run Study
                                </button>
                            </div>
                        </div>

                        <div class="tool-card" data-tool="kinetic-modeling">
                            <div class="tool-header">
                                <i class="fas fa-project-diagram tool-icon"></i>
                                <h3>Kinetic Modeling Tool</h3>
                            </div>
                            <div class="tool-content">
                                <p>Compartmental modeling and tracer kinetics analysis with parameter estimation.</p>
                                <div class="tool-features">
                                    <span class="feature">Compartmental Models</span>
                                    <span class="feature">Parameter Fitting</span>
                                    <span class="feature">Statistical Analysis</span>
                                </div>
                                <button type="button" class="tool-button" onclick="openTool('kinetic-modeling')">
                                    <i class="fas fa-project-diagram"></i> Model Kinetics
                                </button>
                            </div>
                        </div>

                        <div class="tool-card" data-tool="3d-atomic-structure">
                            <div class="tool-header">
                                <i class="fas fa-atom tool-icon"></i>
                                <h3>3D Atomic Structure</h3>
                            </div>
                            <div class="tool-content">
                                <p>Interactive 3D visualization of atomic structure with electron orbitals and nuclear components.</p>
                                <div class="tool-features">
                                    <span class="feature">3D Visualization</span>
                                    <span class="feature">Electron Orbitals</span>
                                    <span class="feature">Interactive Rotation</span>
                                </div>
                                <button type="button" class="tool-button" onclick="openTool('3d-atomic-structure')">
                                    <i class="fas fa-atom"></i> View 3D Atom
                                </button>
                            </div>
                        </div>

                        <div class="tool-card" data-tool="3d-gamma-camera">
                            <div class="tool-header">
                                <i class="fas fa-camera tool-icon"></i>
                                <h3>3D Gamma Camera</h3>
                            </div>
                            <div class="tool-content">
                                <p>Interactive 3D model of gamma camera system with photon detection visualization.</p>
                                <div class="tool-features">
                                    <span class="feature">3D Camera Model</span>
                                    <span class="feature">Photon Tracking</span>
                                    <span class="feature">Real-time Detection</span>
                                </div>
                                <button type="button" class="tool-button" onclick="openTool('3d-gamma-camera')">
                                    <i class="fas fa-camera"></i> View 3D Camera
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- References Section -->
            <section id="references" class="content-section">
                <div class="book-container">
                    <h2><i class="fas fa-citation"></i> Academic References & Citations</h2>
                    <p class="section-description">
                        Comprehensive bibliography and reference materials supporting the academic content.
                    </p>

                    <div class="references-container">
                        <div class="reference-category">
                            <h3><i class="fas fa-book"></i> Foundational Textbooks</h3>
                            <div class="reference-list">
                                <div class="reference-item">
                                    <div class="reference-number">1</div>
                                    <div class="reference-content">
                                        <strong>Cherry, S.R., Sorenson, J.A., & Phelps, M.E. (2012).</strong>
                                        <em>Physics in Nuclear Medicine</em> (4th ed.). Elsevier Saunders.
                                        <div class="reference-note">Comprehensive coverage of nuclear medicine physics principles</div>
                                    </div>
                                </div>
                                <div class="reference-item">
                                    <div class="reference-number">2</div>
                                    <div class="reference-content">
                                        <strong>Saha, G.B. (2013).</strong>
                                        <em>Physics and Radiobiology of Nuclear Medicine</em> (4th ed.). Springer.
                                        <div class="reference-note">Detailed treatment of radiation physics and biological effects</div>
                                    </div>
                                </div>
                                <div class="reference-item">
                                    <div class="reference-number">3</div>
                                    <div class="reference-content">
                                        <strong>Knoll, G.F. (2010).</strong>
                                        <em>Radiation Detection and Measurement</em> (4th ed.). John Wiley & Sons.
                                        <div class="reference-note">Authoritative text on radiation detection principles</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="reference-category">
                            <h3><i class="fas fa-flask"></i> Research Articles</h3>
                            <div class="reference-list">
                                <div class="reference-item">
                                    <div class="reference-number">4</div>
                                    <div class="reference-content">
                                        <strong>Anger, H.O. (1958).</strong>
                                        Scintillation camera. <em>Review of Scientific Instruments</em>, 29(1), 27-33.
                                        <div class="reference-note">Seminal paper on gamma camera development</div>
                                    </div>
                                </div>
                                <div class="reference-item">
                                    <div class="reference-number">5</div>
                                    <div class="reference-content">
                                        <strong>Shepp, L.A., & Logan, B.F. (1974).</strong>
                                        The Fourier reconstruction of a head section. <em>IEEE Transactions on Nuclear Science</em>, 21(3), 21-43.
                                        <div class="reference-note">Fundamental work on tomographic reconstruction</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="reference-category">
                            <h3><i class="fas fa-globe"></i> International Standards</h3>
                            <div class="reference-list">
                                <div class="reference-item">
                                    <div class="reference-number">6</div>
                                    <div class="reference-content">
                                        <strong>IAEA (2014).</strong>
                                        <em>Radiation Protection and Safety of Radiation Sources: International Basic Safety Standards</em>.
                                        IAEA Safety Standards Series No. GSR Part 3.
                                        <div class="reference-note">International radiation safety standards</div>
                                    </div>
                                </div>
                                <div class="reference-item">
                                    <div class="reference-number">7</div>
                                    <div class="reference-content">
                                        <strong>NEMA (2018).</strong>
                                        <em>Performance Measurements of Gamma Cameras</em>.
                                        NEMA Standards Publication NU 1-2018.
                                        <div class="reference-note">Industry standards for gamma camera performance</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Resources Section -->
            <section id="resources" class="content-section">
                <div class="book-container">
                    <h2><i class="fas fa-download"></i> Educational Resources & Downloads</h2>
                    <p class="section-description">
                        Supplementary materials, software tools, and educational resources for enhanced learning.
                    </p>

                    <div class="resources-grid">
                        <div class="resource-category">
                            <h3><i class="fas fa-file-pdf"></i> Lecture Materials</h3>
                            <div class="resource-items">
                                <div class="resource-item">
                                    <i class="fas fa-file-pdf"></i>
                                    <div class="resource-info">
                                        <h4>Complete Lecture Slides (PDF)</h4>
                                        <p>All 12 chapters in high-resolution PDF format</p>
                                        <span class="file-size">45.2 MB</span>
                                    </div>
                                    <button type="button" class="download-btn">
                                        <i class="fas fa-download"></i> Download
                                    </button>
                                </div>
                                <div class="resource-item">
                                    <i class="fas fa-file-powerpoint"></i>
                                    <div class="resource-info">
                                        <h4>PowerPoint Presentations</h4>
                                        <p>Editable PowerPoint files for instructors</p>
                                        <span class="file-size">128.7 MB</span>
                                    </div>
                                    <button type="button" class="download-btn">
                                        <i class="fas fa-download"></i> Download
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="resource-category">
                            <h3><i class="fas fa-code"></i> Software Tools</h3>
                            <div class="resource-items">
                                <div class="resource-item">
                                    <i class="fas fa-desktop"></i>
                                    <div class="resource-info">
                                        <h4>Nuclear Medicine Simulator</h4>
                                        <p>Comprehensive simulation software package</p>
                                        <span class="file-size">256.3 MB</span>
                                    </div>
                                    <button type="button" class="download-btn">
                                        <i class="fas fa-download"></i> Download
                                    </button>
                                </div>
                                <div class="resource-item">
                                    <i class="fas fa-calculator"></i>
                                    <div class="resource-info">
                                        <h4>Dosimetry Calculator</h4>
                                        <p>MIRD-based dose calculation tool</p>
                                        <span class="file-size">12.8 MB</span>
                                    </div>
                                    <button type="button" class="download-btn">
                                        <i class="fas fa-download"></i> Download
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>

                <div class="part" onclick="showChapter('part2')">
                    <div class="part-title">الجزء الثاني: رحلة المريض خطوة بخطوة</div>
                    <div class="chapter" onclick="event.stopPropagation(); showChapter('chapter3')">
                        <div class="chapter-title">الفصل الثالث: رحلة التشخيص</div>
                        <div class="chapter-content">كيف نرى المرض من الداخل؟</div>
                    </div>
                    <div class="chapter" onclick="event.stopPropagation(); showChapter('chapter4')">
                        <div class="chapter-title">الفصل الرابع: رحلة العلاج</div>
                        <div class="chapter-content">الرصاصة السحرية التي تستهدف السرطان</div>
                    </div>
                </div>

                <div class="part" onclick="showChapter('part3')">
                    <div class="part-title">الجزء الثالث: هل هو آمن؟</div>
                    <div class="chapter" onclick="event.stopPropagation(); showChapter('chapter5')">
                        <div class="chapter-title">الفصل الخامس: الجرعة الإشعاعية</div>
                        <div class="chapter-content">أقل مما تتوقع!</div>
                    </div>
                    <div class="chapter" onclick="event.stopPropagation(); showChapter('chapter6')">
                        <div class="chapter-title">الفصل السادس: جيش من الخبراء</div>
                        <div class="chapter-content">في خدمتك</div>
                    </div>
                </div>

                <div class="part" onclick="showChapter('part4')">
                    <div class="part-title">الجزء الرابع: نظرة إلى المستقبل</div>
                    <div class="chapter" onclick="event.stopPropagation(); showChapter('chapter7')">
                        <div class="chapter-title">الفصل السابع: طب أكثر دقة وشخصية</div>
                        <div class="chapter-content">دور الذكاء الاصطناعي والتطوير المستقبلي</div>
                    </div>
                </div>
            </div>

            <div class="features">
                <div class="feature-card">
                    <div class="feature-icon">🔬</div>
                    <div class="feature-title">لغة مبسطة</div>
                    <div class="feature-description">شرح المفاهيم المعقدة بأسلوب قصصي سهل الفهم</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <div class="feature-title">تشبيهات واقعية</div>
                    <div class="feature-description">استخدام الجواسيس الطبية والرصاصات السحرية</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">❤️</div>
                    <div class="feature-title">قصص إنسانية</div>
                    <div class="feature-description">تجارب حقيقية ملهمة لمرضى تم شفاؤهم</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🛡️</div>
                    <div class="feature-title">الأمان أولاً</div>
                    <div class="feature-description">شرح مفصل لإجراءات الأمان والحماية</div>
                </div>
            </div>

            <div class="interactive-section">
                <h3>اكتشف المزيد</h3>
                <p>تفاعل مع محتوى الكتاب واكتشف عالم الطب النووي</p>
                <button class="btn" onclick="showModal('faq')">الأسئلة الشائعة</button>
                <button class="btn" onclick="showModal('glossary')">مسرد المصطلحات</button>
                <button class="btn" onclick="showModal('safety')">معلومات الأمان</button>
            </div>
        </div>
    </div>

    <!-- Modal for FAQ -->
    <div id="faqModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>الأسئلة الشائعة</h2>
            <div class="qa-section">
                <div class="question">هل سأصبح مشعاً بعد الفحص؟</div>
                <div class="answer">لا، إطلاقاً. المادة المشعة المستخدمة في الطب النووي تكون بكميات ضئيلة جداً وتختفي من الجسم بسرعة. لن تصبح مشعاً ولن تشكل خطراً على الآخرين.</div>
            </div>
            <div class="qa-section">
                <div class="question">هل يمكنني الاقتراب من الأطفال بعد الفحص؟</div>
                <div class="answer">نعم، في معظم الحالات يمكنك العودة إلى حياتك الطبيعية فوراً. في حالات نادرة جداً قد ينصح الطبيب بتجنب الاقتراب من الأطفال والحوامل لفترة قصيرة.</div>
            </div>
            <div class="qa-section">
                <div class="question">هل الفحص مؤلم؟</div>
                <div class="answer">لا، الفحص غير مؤلم تماماً. ستشعر فقط بوخزة بسيطة عند الحقن، مثل أي حقنة عادية، ثم لن تشعر بأي شيء.</div>
            </div>
        </div>
    </div>

    <!-- Modal for Glossary -->
    <div id="glossaryModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>مسرد المصطلحات</h2>
            <div class="qa-section">
                <div class="question">الجواسيس الطبية</div>
                <div class="answer">مواد مشعة آمنة تُحقن في الجسم وتذهب إلى الأعضاء المطلوب فحصها لتعطي صورة واضحة عن وظائفها.</div>
            </div>
            <div class="qa-section">
                <div class="question">الرصاصة السحرية</div>
                <div class="answer">دواء مشع علاجي يستهدف الخلايا السرطانية بدقة عالية ويدمرها دون إلحاق ضرر كبير بالخلايا السليمة.</div>
            </div>
            <div class="qa-section">
                <div class="question">PET/CT</div>
                <div class="answer">جهاز تصوير متطور يجمع بين التصوير الوظيفي والتشريحي ليعطي صورة شاملة عن الجسم.</div>
            </div>
        </div>
    </div>

    <!-- Modal for Safety -->
    <div id="safetyModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>معلومات الأمان</h2>
            <div class="qa-section">
                <div class="question">مبدأ ALARA</div>
                <div class="answer">يعني "أقل ما يمكن تحقيقه بشكل معقول" - الأطباء يستخدمون أقل جرعة إشعاع ممكنة للحصول على المعلومة المطلوبة.</div>
            </div>
            <div class="qa-section">
                <div class="question">مقارنة الجرعات</div>
                <div class="answer">جرعة الإشعاع من فحص طبي نووي تعادل ما نتعرض له طبيعياً خلال بضع سنوات، أو عدة رحلات طيران طويلة.</div>
            </div>
            <div class="qa-section">
                <div class="question">فريق الخبراء</div>
                <div class="answer">يشرف على كل فحص فريق من الخبراء: الطبيب النووي، الفيزيائي الطبي، الكيميائي الإشعاعي، والتقني المتخصص.</div>
            </div>
        </div>
    </div>

    <script>
        // Global Application State
        const NuclearMedicineApp = {
            currentSection: 'overview',
            currentChapter: null,
            currentPresentation: null,
            animations: [],
            tools: {},
            simulations: {},
            quizzes: {},
            virtualLab: {},
            userData: {
                progress: {},
                scores: {},
                timeSpent: {},
                preferences: {}
            },
            realTimeData: {
                decaySimulation: null,
                doseCalculation: null,
                imageReconstruction: null
            },

            // Initialize the application
            init() {
                this.setupEventListeners();
                this.createFloatingAtoms();
                this.animateProgress();
                this.initializeTools();
                this.setupMathJax();
                this.initializeAdvancedFeatures();
                this.startRealTimeUpdates();
                this.loadUserProgress();
                this.setupKeyboardShortcuts();
                console.log('Nuclear Medicine Physics Educational Module Initialized with Advanced Features');
            },

            // Initialize advanced interactive features
            initializeAdvancedFeatures() {
                this.initializeParticleSystem();
                this.setupDynamicContent();
                this.initializeProgressTracking();
                this.setupNotificationSystem();
                this.initializeSearchFunctionality();
                this.setupThemeCustomization();
            },

            // Initialize particle system for enhanced visuals
            initializeParticleSystem() {
                this.particleSystem = new ParticleSystem();
                this.particleSystem.init();
            },

            // Setup dynamic content generation
            setupDynamicContent() {
                this.contentGenerator = new DynamicContentGenerator();
                this.contentGenerator.init();
            },

            // Initialize progress tracking
            initializeProgressTracking() {
                this.progressTracker = new ProgressTracker();
                this.progressTracker.init();
            },

            // Setup notification system
            setupNotificationSystem() {
                this.notifications = new NotificationSystem();
                this.notifications.init();
            },

            // Initialize search functionality
            initializeSearchFunctionality() {
                this.searchEngine = new SearchEngine();
                this.searchEngine.init();
            },

            // Setup theme customization
            setupThemeCustomization() {
                this.themeManager = new ThemeManager();
                this.themeManager.init();
            },

            // Start real-time updates
            startRealTimeUpdates() {
                setInterval(() => {
                    this.updateRealTimeData();
                    this.updateAnimations();
                    this.checkUserActivity();
                }, 100); // Update every 100ms for smooth animations
            },

            // Update real-time data
            updateRealTimeData() {
                if (this.realTimeData.decaySimulation) {
                    this.realTimeData.decaySimulation.update();
                }
                if (this.realTimeData.doseCalculation) {
                    this.realTimeData.doseCalculation.update();
                }
                if (this.realTimeData.imageReconstruction) {
                    this.realTimeData.imageReconstruction.update();
                }
            },

            // Update animations
            updateAnimations() {
                this.animations.forEach(animation => {
                    if (animation.isActive) {
                        animation.update();
                    }
                });
            },

            // Check user activity for adaptive learning
            checkUserActivity() {
                const now = Date.now();
                if (this.userData.lastActivity && (now - this.userData.lastActivity) > 300000) { // 5 minutes
                    this.showEngagementPrompt();
                }
            },

            // Show engagement prompt
            showEngagementPrompt() {
                this.notifications.show({
                    type: 'info',
                    title: 'Take a Break?',
                    message: 'You\'ve been studying for a while. Would you like to try an interactive quiz or simulation?',
                    actions: [
                        { text: 'Take Quiz', action: () => this.startRandomQuiz() },
                        { text: 'Run Simulation', action: () => this.startRandomSimulation() },
                        { text: 'Continue', action: () => this.notifications.hide() }
                    ]
                });
            },

            // Load user progress
            loadUserProgress() {
                const savedProgress = localStorage.getItem('nuclearMedicineProgress');
                if (savedProgress) {
                    this.userData = { ...this.userData, ...JSON.parse(savedProgress) };
                    this.updateProgressDisplay();
                }
            },

            // Save user progress
            saveUserProgress() {
                localStorage.setItem('nuclearMedicineProgress', JSON.stringify(this.userData));
            },

            // Setup keyboard shortcuts
            setupKeyboardShortcuts() {
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey || e.metaKey) {
                        switch(e.key) {
                            case 'h':
                                e.preventDefault();
                                this.showKeyboardShortcuts();
                                break;
                            case 's':
                                e.preventDefault();
                                this.searchEngine.focus();
                                break;
                            case 'q':
                                e.preventDefault();
                                this.startRandomQuiz();
                                break;
                            case 't':
                                e.preventDefault();
                                this.themeManager.toggleTheme();
                                break;
                        }
                    }

                    // Navigation shortcuts
                    if (e.altKey) {
                        switch(e.key) {
                            case '1':
                                e.preventDefault();
                                this.showSection('overview');
                                break;
                            case '2':
                                e.preventDefault();
                                this.showSection('chapters');
                                break;
                            case '3':
                                e.preventDefault();
                                this.showSection('presentations');
                                break;
                            case '4':
                                e.preventDefault();
                                this.showSection('interactive');
                                break;
                        }
                    }
                });
            },

            // Setup all event listeners
            setupEventListeners() {
                // Navigation menu
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.addEventListener('click', (e) => {
                        const section = e.currentTarget.dataset.section;
                        this.showSection(section);
                    });
                });

                // Chapter cards
                document.querySelectorAll('.chapter-card').forEach(card => {
                    card.addEventListener('click', (e) => {
                        const chapter = e.currentTarget.dataset.chapter;
                        this.showChapterDetails(chapter);
                    });
                });

                // Presentation cards
                document.querySelectorAll('.presentation-card').forEach(card => {
                    card.addEventListener('click', (e) => {
                        const presentation = e.currentTarget.dataset.presentation;
                        this.startPresentation(presentation);
                    });
                });

                // Tool buttons
                document.querySelectorAll('.tool-button').forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.stopPropagation();
                        // Tool opening handled by onclick attribute for now
                    });
                });

                // Modal close events
                document.querySelectorAll('.close').forEach(closeBtn => {
                    closeBtn.addEventListener('click', function() {
                        this.closest('.modal').style.display = 'none';
                    });
                });

                // Close modal when clicking outside
                document.querySelectorAll('.modal').forEach(modal => {
                    modal.addEventListener('click', function(e) {
                        if (e.target === this) {
                            this.style.display = 'none';
                        }
                    });
                });

                // Responsive navigation toggle
                const navToggle = document.getElementById('navToggle');
                const navMenu = document.getElementById('navMenu');
                if (navToggle && navMenu) {
                    navToggle.addEventListener('click', () => {
                        navMenu.classList.toggle('active');
                    });
                }
            },

            // Show specific section
            showSection(sectionId) {
                // Hide all sections
                document.querySelectorAll('.content-section').forEach(section => {
                    section.classList.remove('active');
                });

                // Show target section
                const targetSection = document.getElementById(sectionId);
                if (targetSection) {
                    targetSection.classList.add('active');
                    this.currentSection = sectionId;
                }

                // Update navigation
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                document.querySelector(`[data-section="${sectionId}"]`).classList.add('active');

                // Trigger section-specific animations
                this.triggerSectionAnimations(sectionId);
            },

            // Create floating atoms animation
            createFloatingAtoms() {
                const atomsContainer = document.querySelector('.floating-atoms');
                if (!atomsContainer) return;

                for (let i = 0; i < 20; i++) {
                    const atom = document.createElement('div');
                    atom.className = 'atom';
                    atom.style.left = Math.random() * 100 + '%';
                    atom.style.top = Math.random() * 100 + '%';
                    atom.style.animationDelay = Math.random() * 6 + 's';
                    atom.style.animationDuration = (Math.random() * 4 + 4) + 's';
                    atomsContainer.appendChild(atom);
                }
            }

            // Progress bar animation
            animateProgress() {
                const progressFill = document.getElementById('progressFill');
                if (!progressFill) return;

                let width = 0;
                const interval = setInterval(() => {
                    if (width >= 100) {
                        clearInterval(interval);
                    } else {
                        width += 2;
                        progressFill.style.width = width + '%';
                    }
                }, 50);
            },

            // Show chapter details with enhanced information
            showChapterDetails(chapterId) {
                const chapterData = this.getChapterData(chapterId);
                if (!chapterData) return;

                // Create detailed chapter modal
                const modal = this.createChapterModal(chapterData);
                document.body.appendChild(modal);
                modal.style.display = 'block';
            },

            // Get chapter data
            getChapterData(chapterId) {
                const chapters = {
                    '1': {
                        title: 'Atomic Structure and Nuclear Properties',
                        content: `
                            <h3>Chapter 1: Atomic Structure and Nuclear Properties</h3>
                            <p>This chapter provides a comprehensive foundation in atomic and nuclear physics essential for understanding nuclear medicine applications.</p>

                            <h4>Learning Objectives:</h4>
                            <ul>
                                <li>Understand the quantum mechanical model of the atom</li>
                                <li>Analyze nuclear composition and properties</li>
                                <li>Calculate binding energies and nuclear stability</li>
                                <li>Evaluate mass-energy relationships</li>
                            </ul>

                            <h4>Key Concepts:</h4>
                            <div class="concept-grid">
                                <div class="concept-item">
                                    <h5>Atomic Models</h5>
                                    <p>Evolution from Rutherford to quantum mechanical models</p>
                                </div>
                                <div class="concept-item">
                                    <h5>Nuclear Composition</h5>
                                    <p>Protons, neutrons, and nuclear forces</p>
                                </div>
                                <div class="concept-item">
                                    <h5>Binding Energy</h5>
                                    <p>Mass defect and nuclear stability</p>
                                </div>
                            </div>

                            <h4>Mathematical Framework:</h4>
                            <div class="equation-block">
                                <p><strong>Mass-Energy Equivalence:</strong></p>
                                <div class="equation">E = mc²</div>
                                <p><strong>Binding Energy:</strong></p>
                                <div class="equation">BE = [Z·m_H + N·m_n - M]c²</div>
                                <p><strong>Semi-empirical Mass Formula:</strong></p>
                                <div class="equation">BE = a_v A - a_s A^(2/3) - a_c Z²/A^(1/3) - a_A (N-Z)²/A + δ(N,Z)</div>
                            </div>
                        `,
                        figures: [
                            'Atomic structure diagrams',
                            'Nuclear binding energy curve',
                            'Chart of nuclides',
                            'Mass spectrometry data'
                        ],
                        tables: [
                            'Fundamental constants',
                            'Nuclear properties of medical isotopes',
                            'Binding energies per nucleon'
                        ]
                    },
                    '2': {
                        title: 'Radioactive Decay and Nuclear Transformations',
                        content: `
                            <h3>Chapter 2: Radioactive Decay and Nuclear Transformations</h3>
                            <p>Comprehensive study of radioactive decay processes and nuclear transformation mechanisms.</p>

                            <h4>Decay Modes:</h4>
                            <ul>
                                <li>Alpha decay (α)</li>
                                <li>Beta minus decay (β⁻)</li>
                                <li>Beta plus decay (β⁺)</li>
                                <li>Electron capture (EC)</li>
                                <li>Gamma emission (γ)</li>
                                <li>Internal conversion</li>
                            </ul>

                            <h4>Mathematical Framework:</h4>
                            <div class="equation-block">
                                <p><strong>Decay Law:</strong></p>
                                <div class="equation">N(t) = N₀e^(-λt)</div>
                                <p><strong>Activity:</strong></p>
                                <div class="equation">A(t) = λN(t) = A₀e^(-λt)</div>
                                <p><strong>Half-life:</strong></p>
                                <div class="equation">t₁/₂ = ln(2)/λ = 0.693/λ</div>
                            </div>
                        `
                    }
                    // Add more chapters as needed
                };
                return chapters[chapterId];
            },

            // Create chapter modal
            createChapterModal(chapterData) {
                const modal = document.createElement('div');
                modal.className = 'modal chapter-modal';
                modal.innerHTML = `
                    <div class="modal-content chapter-content">
                        <span class="close">&times;</span>
                        <div class="chapter-details">
                            ${chapterData.content}
                        </div>
                        <div class="chapter-actions">
                            <button type="button" class="btn-primary" onclick="NuclearMedicineApp.startChapterQuiz('${chapterData.title}')">
                                <i class="fas fa-question-circle"></i> Take Quiz
                            </button>
                            <button type="button" class="btn-secondary" onclick="NuclearMedicineApp.downloadChapter('${chapterData.title}')">
                                <i class="fas fa-download"></i> Download PDF
                            </button>
                        </div>
                    </div>
                `;

                // Add close functionality
                modal.querySelector('.close').addEventListener('click', () => {
                    modal.style.display = 'none';
                    document.body.removeChild(modal);
                });

                return modal;
            },

            // Start presentation
            startPresentation(presentationId) {
                console.log(`Starting presentation: ${presentationId}`);
                // This would integrate with a presentation framework
                this.showPresentationModal(presentationId);
            },

            // Show presentation modal
            showPresentationModal(presentationId) {
                const presentations = {
                    'nuclear-physics': {
                        title: 'Nuclear Physics Fundamentals',
                        slides: 45,
                        duration: '60 minutes',
                        description: 'Interactive presentation covering atomic structure, nuclear properties, and radioactive decay with animations and simulations.'
                    },
                    'detection-systems': {
                        title: 'Radiation Detection Systems',
                        slides: 52,
                        duration: '75 minutes',
                        description: 'Comprehensive overview of detection principles, gamma cameras, and imaging systems with interactive demonstrations.'
                    }
                    // Add more presentations
                };

                const presentation = presentations[presentationId];
                if (!presentation) return;

                const modal = document.createElement('div');
                modal.className = 'modal presentation-modal';
                modal.innerHTML = `
                    <div class="modal-content presentation-content">
                        <span class="close">&times;</span>
                        <h2>${presentation.title}</h2>
                        <div class="presentation-info">
                            <p><strong>Slides:</strong> ${presentation.slides}</p>
                            <p><strong>Duration:</strong> ${presentation.duration}</p>
                            <p>${presentation.description}</p>
                        </div>
                        <div class="presentation-actions">
                            <button type="button" class="btn-primary" onclick="NuclearMedicineApp.launchFullPresentation('${presentationId}')">
                                <i class="fas fa-play"></i> Start Full Presentation
                            </button>
                            <button type="button" class="btn-secondary" onclick="NuclearMedicineApp.previewPresentation('${presentationId}')">
                                <i class="fas fa-eye"></i> Preview Slides
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                modal.style.display = 'block';

                modal.querySelector('.close').addEventListener('click', () => {
                    modal.style.display = 'none';
                    document.body.removeChild(modal);
                });
            }

            // Initialize interactive tools
            initializeTools() {
                this.tools = {
                    'decay-simulator': new DecaySimulator(),
                    'dose-calculator': new DoseCalculator(),
                    'image-reconstruction': new ImageReconstruction(),
                    'detector-response': new DetectorResponse(),
                    'phantom-studies': new PhantomStudies(),
                    'kinetic-modeling': new KineticModeling(),
                    '3d-atomic-structure': new AtomicStructure3DTool(),
                    '3d-gamma-camera': new GammaCamera3DTool()
                };
            },

            // Setup MathJax for equation rendering
            setupMathJax() {
                if (window.MathJax) {
                    MathJax.typesetPromise().then(() => {
                        console.log('MathJax equations rendered');
                    }).catch((err) => console.log('MathJax error: ' + err.message));
                }
            },

            // Trigger section-specific animations
            triggerSectionAnimations(sectionId) {
                switch(sectionId) {
                    case 'overview':
                        this.animateOverviewCards();
                        break;
                    case 'chapters':
                        this.animateChapterCards();
                        break;
                    case 'presentations':
                        this.animatePresentationCards();
                        break;
                    case 'interactive':
                        this.animateToolCards();
                        break;
                }
            },

            // Animate overview cards
            animateOverviewCards() {
                const cards = document.querySelectorAll('.overview-card');
                cards.forEach((card, index) => {
                    setTimeout(() => {
                        card.style.animation = 'fadeInUp 0.6s ease forwards';
                    }, index * 200);
                });
            },

            // Animate chapter cards
            animateChapterCards() {
                const cards = document.querySelectorAll('.chapter-card');
                cards.forEach((card, index) => {
                    setTimeout(() => {
                        card.style.animation = 'fadeInUp 0.6s ease forwards';
                    }, index * 150);
                });
            },

            // Animate presentation cards
            animatePresentationCards() {
                const cards = document.querySelectorAll('.presentation-card');
                cards.forEach((card, index) => {
                    setTimeout(() => {
                        card.style.animation = 'fadeInUp 0.6s ease forwards';
                    }, index * 200);
                });
            },

            // Animate tool cards
            animateToolCards() {
                const cards = document.querySelectorAll('.tool-card');
                cards.forEach((card, index) => {
                    setTimeout(() => {
                        card.style.animation = 'fadeInUp 0.6s ease forwards';
                    }, index * 150);
                });
            },

            // Launch full presentation
            launchFullPresentation(presentationId) {
                console.log(`Launching full presentation: ${presentationId}`);
                // This would integrate with a presentation framework like Reveal.js
                alert(`Full presentation mode for ${presentationId} would be launched here. This requires integration with a presentation framework.`);
            },

            // Preview presentation
            previewPresentation(presentationId) {
                console.log(`Previewing presentation: ${presentationId}`);
                alert(`Presentation preview for ${presentationId} would be shown here.`);
            },

            // Start chapter quiz
            startChapterQuiz(chapterTitle) {
                console.log(`Starting quiz for: ${chapterTitle}`);
                alert(`Interactive quiz for "${chapterTitle}" would be launched here.`);
            },

            // Download chapter
            downloadChapter(chapterTitle) {
                console.log(`Downloading chapter: ${chapterTitle}`);
                alert(`PDF download for "${chapterTitle}" would be initiated here.`);
            }
        };

        // Advanced Interactive Classes

        // Particle System for Enhanced Visuals
        class ParticleSystem {
            constructor() {
                this.particles = [];
                this.canvas = null;
                this.ctx = null;
                this.animationId = null;
            }

            init() {
                this.createCanvas();
                this.generateParticles();
                this.animate();
            }

            createCanvas() {
                this.canvas = document.createElement('canvas');
                this.canvas.id = 'particleCanvas';
                this.canvas.style.position = 'fixed';
                this.canvas.style.top = '0';
                this.canvas.style.left = '0';
                this.canvas.style.width = '100%';
                this.canvas.style.height = '100%';
                this.canvas.style.pointerEvents = 'none';
                this.canvas.style.zIndex = '-1';
                this.canvas.style.opacity = '0.3';

                this.ctx = this.canvas.getContext('2d');
                this.resizeCanvas();

                document.body.appendChild(this.canvas);

                window.addEventListener('resize', () => this.resizeCanvas());
            }

            resizeCanvas() {
                this.canvas.width = window.innerWidth;
                this.canvas.height = window.innerHeight;
            }

            generateParticles() {
                const particleCount = Math.min(50, Math.floor(window.innerWidth / 30));
                for (let i = 0; i < particleCount; i++) {
                    this.particles.push({
                        x: Math.random() * this.canvas.width,
                        y: Math.random() * this.canvas.height,
                        vx: (Math.random() - 0.5) * 0.5,
                        vy: (Math.random() - 0.5) * 0.5,
                        radius: Math.random() * 3 + 1,
                        opacity: Math.random() * 0.5 + 0.2,
                        color: `hsl(${Math.random() * 60 + 200}, 70%, 60%)`
                    });
                }
            }

            animate() {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

                this.particles.forEach(particle => {
                    // Update position
                    particle.x += particle.vx;
                    particle.y += particle.vy;

                    // Bounce off edges
                    if (particle.x < 0 || particle.x > this.canvas.width) particle.vx *= -1;
                    if (particle.y < 0 || particle.y > this.canvas.height) particle.vy *= -1;

                    // Draw particle
                    this.ctx.beginPath();
                    this.ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
                    this.ctx.fillStyle = particle.color;
                    this.ctx.globalAlpha = particle.opacity;
                    this.ctx.fill();
                });

                this.animationId = requestAnimationFrame(() => this.animate());
            }

            destroy() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }
                if (this.canvas) {
                    document.body.removeChild(this.canvas);
                }
            }
        }

        // Dynamic Content Generator
        class DynamicContentGenerator {
            constructor() {
                this.templates = {};
                this.cache = new Map();
            }

            init() {
                this.loadTemplates();
                this.setupContentUpdates();
            }

            loadTemplates() {
                this.templates = {
                    equation: (data) => `
                        <div class="dynamic-equation" data-equation="${data.id}">
                            <h5>${data.title}</h5>
                            <div class="equation-display">${data.equation}</div>
                            <div class="equation-explanation">${data.explanation}</div>
                            <button type="button" class="btn-interactive" onclick="this.showDerivation('${data.id}')">
                                Show Derivation
                            </button>
                        </div>
                    `,
                    simulation: (data) => `
                        <div class="dynamic-simulation" data-sim="${data.id}">
                            <h5>${data.title}</h5>
                            <div class="simulation-preview">${data.preview}</div>
                            <div class="simulation-controls">${data.controls}</div>
                            <button type="button" class="btn-interactive" onclick="this.launchSimulation('${data.id}')">
                                Launch Simulation
                            </button>
                        </div>
                    `,
                    quiz: (data) => `
                        <div class="dynamic-quiz" data-quiz="${data.id}">
                            <h5>${data.title}</h5>
                            <div class="quiz-question">${data.question}</div>
                            <div class="quiz-options">${data.options.map((opt, i) =>
                                `<label><input type="radio" name="q${data.id}" value="${i}"> ${opt}</label>`
                            ).join('')}</div>
                            <button type="button" class="btn-interactive" onclick="this.checkAnswer('${data.id}')">
                                Check Answer
                            </button>
                        </div>
                    `
                };
            }

            setupContentUpdates() {
                // Update content based on user interaction
                setInterval(() => {
                    this.updateDynamicContent();
                }, 30000); // Update every 30 seconds
            }

            updateDynamicContent() {
                const activeSection = NuclearMedicineApp.currentSection;
                const userLevel = this.getUserLevel();

                // Generate contextual content
                this.generateContextualEquations(activeSection, userLevel);
                this.generateRelevantQuizzes(activeSection, userLevel);
                this.updateProgressIndicators();
            }

            generateContextualEquations(section, level) {
                const equations = this.getEquationsForSection(section, level);
                const container = document.querySelector('.dynamic-content-equations');

                if (container && equations.length > 0) {
                    const randomEquation = equations[Math.floor(Math.random() * equations.length)];
                    container.innerHTML = this.templates.equation(randomEquation);
                }
            }

            getUserLevel() {
                const progress = NuclearMedicineApp.userData.progress;
                const totalProgress = Object.values(progress).reduce((sum, val) => sum + val, 0);

                if (totalProgress < 25) return 'beginner';
                if (totalProgress < 75) return 'intermediate';
                return 'advanced';
            }

            getEquationsForSection(section, level) {
                const equationDatabase = {
                    'chapters': {
                        'beginner': [
                            {
                                id: 'decay_law',
                                title: 'Radioactive Decay Law',
                                equation: 'N(t) = N₀e^(-λt)',
                                explanation: 'The fundamental law describing radioactive decay over time.'
                            }
                        ],
                        'intermediate': [
                            {
                                id: 'activity_calc',
                                title: 'Activity Calculation',
                                equation: 'A(t) = λN(t) = A₀e^(-λt)',
                                explanation: 'Relationship between activity and number of radioactive nuclei.'
                            }
                        ],
                        'advanced': [
                            {
                                id: 'bateman_eq',
                                title: 'Bateman Equation',
                                equation: 'N₂(t) = (λ₁N₁₀)/(λ₂-λ₁)[e^(-λ₁t) - e^(-λ₂t)]',
                                explanation: 'Solution for radioactive decay chains.'
                            }
                        ]
                    }
                };

                return equationDatabase[section]?.[level] || [];
            }
        }

        // Interactive Tool Classes
        class DecaySimulator {
            constructor() {
                this.isotopes = {
                    'Tc-99m': { halfLife: 6.01, decayConstant: 0.1155, units: 'hours' },
                    'I-131': { halfLife: 8.02, decayConstant: 0.0864, units: 'days' },
                    'F-18': { halfLife: 1.83, decayConstant: 0.3783, units: 'hours' },
                    'Co-60': { halfLife: 5.27, decayConstant: 0.1315, units: 'years' },
                    'Cs-137': { halfLife: 30.17, decayConstant: 0.023, units: 'years' }
                };
                this.isRunning = false;
                this.currentData = [];
                this.chart = null;
            }

            launch() {
                console.log('Launching Advanced Decay Simulator');
                this.createAdvancedSimulatorInterface();
            }

            createAdvancedSimulatorInterface() {
                const modal = document.createElement('div');
                modal.className = 'modal tool-modal decay-simulator-modal';
                modal.innerHTML = `
                    <div class="modal-content simulator-content">
                        <span class="close">&times;</span>
                        <h2><i class="fas fa-atom"></i> Advanced Radioactive Decay Simulator</h2>

                        <div class="simulator-tabs">
                            <button type="button" class="tab-button active" data-tab="single">Single Isotope</button>
                            <button type="button" class="tab-button" data-tab="chain">Decay Chain</button>
                            <button type="button" class="tab-button" data-tab="comparison">Compare Isotopes</button>
                        </div>

                        <div class="tab-content" id="single-tab">
                            <div class="simulator-controls">
                                <div class="control-group">
                                    <label>Select Isotope:</label>
                                    <select id="isotopeSelect">
                                        <option value="Tc-99m">Tc-99m (t₁/₂ = 6.01 h)</option>
                                        <option value="I-131">I-131 (t₁/₂ = 8.02 d)</option>
                                        <option value="F-18">F-18 (t₁/₂ = 1.83 h)</option>
                                        <option value="Co-60">Co-60 (t₁/₂ = 5.27 y)</option>
                                        <option value="Cs-137">Cs-137 (t₁/₂ = 30.17 y)</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label>Initial Activity (MBq):</label>
                                    <input type="range" id="initialActivitySlider" min="1" max="1000" value="100">
                                    <input type="number" id="initialActivity" value="100" min="1" max="1000">
                                </div>
                                <div class="control-group">
                                    <label>Time Period:</label>
                                    <input type="range" id="timePeriodSlider" min="1" max="168" value="24">
                                    <input type="number" id="timePeriod" value="24" min="1" max="168">
                                    <select id="timeUnits">
                                        <option value="hours">Hours</option>
                                        <option value="days">Days</option>
                                        <option value="years">Years</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label>Animation Speed:</label>
                                    <input type="range" id="animationSpeed" min="1" max="10" value="5">
                                    <span id="speedDisplay">5x</span>
                                </div>
                                <div class="simulation-buttons">
                                    <button type="button" class="btn-primary" id="runSimBtn">
                                        <i class="fas fa-play"></i> Run Simulation
                                    </button>
                                    <button type="button" class="btn-secondary" id="pauseSimBtn" disabled>
                                        <i class="fas fa-pause"></i> Pause
                                    </button>
                                    <button type="button" class="btn-secondary" id="resetSimBtn">
                                        <i class="fas fa-redo"></i> Reset
                                    </button>
                                    <button type="button" class="btn-success" id="exportDataBtn">
                                        <i class="fas fa-download"></i> Export Data
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="simulation-display">
                            <div class="chart-container">
                                <canvas id="decayChart" width="800" height="400"></canvas>
                            </div>
                            <div class="real-time-data">
                                <div class="data-panel">
                                    <h4>Real-time Values</h4>
                                    <div class="data-grid">
                                        <div class="data-item">
                                            <span class="data-label">Current Activity:</span>
                                            <span class="data-value" id="currentActivity">100.0 MBq</span>
                                        </div>
                                        <div class="data-item">
                                            <span class="data-label">Remaining Nuclei:</span>
                                            <span class="data-value" id="remainingNuclei">6.02×10²³</span>
                                        </div>
                                        <div class="data-item">
                                            <span class="data-label">Decay Constant:</span>
                                            <span class="data-value" id="decayConstant">0.1155 h⁻¹</span>
                                        </div>
                                        <div class="data-item">
                                            <span class="data-label">Half-lives Elapsed:</span>
                                            <span class="data-value" id="halfLivesElapsed">0.0</span>
                                        </div>
                                        <div class="data-item">
                                            <span class="data-label">Percentage Remaining:</span>
                                            <span class="data-value" id="percentageRemaining">100.0%</span>
                                        </div>
                                        <div class="data-item">
                                            <span class="data-label">Time Elapsed:</span>
                                            <span class="data-value" id="timeElapsed">0.0 h</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="equation-panel">
                                    <h4>Active Equations</h4>
                                    <div class="equation-display">
                                        <div class="equation">N(t) = N₀e^(-λt)</div>
                                        <div class="equation">A(t) = λN(t)</div>
                                        <div class="equation">t₁/₂ = ln(2)/λ</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="simulation-results">
                            <div class="results-tabs">
                                <button type="button" class="result-tab active" data-result="table">Data Table</button>
                                <button type="button" class="result-tab" data-result="statistics">Statistics</button>
                                <button type="button" class="result-tab" data-result="analysis">Analysis</button>
                            </div>
                            <div class="results-content">
                                <div id="dataTable" class="result-panel active">
                                    <table class="data-table">
                                        <thead>
                                            <tr>
                                                <th>Time</th>
                                                <th>Activity (MBq)</th>
                                                <th>Nuclei Count</th>
                                                <th>% Remaining</th>
                                            </tr>
                                        </thead>
                                        <tbody id="dataTableBody">
                                        </tbody>
                                    </table>
                                </div>
                                <div id="statistics" class="result-panel">
                                    <div class="stats-grid">
                                        <div class="stat-item">
                                            <h5>Mean Lifetime</h5>
                                            <span id="meanLifetime">-</span>
                                        </div>
                                        <div class="stat-item">
                                            <h5>Total Decays</h5>
                                            <span id="totalDecays">-</span>
                                        </div>
                                        <div class="stat-item">
                                            <h5>Decay Rate</h5>
                                            <span id="decayRate">-</span>
                                        </div>
                                    </div>
                                </div>
                                <div id="analysis" class="result-panel">
                                    <div class="analysis-content">
                                        <h5>Simulation Analysis</h5>
                                        <div id="analysisText"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                modal.style.display = 'block';

                // Setup event listeners
                this.setupSimulatorEventListeners(modal);

                modal.querySelector('.close').addEventListener('click', () => {
                    this.stopSimulation();
                    modal.style.display = 'none';
                    document.body.removeChild(modal);
                });
            }

            setupSimulatorEventListeners(modal) {
                // Slider synchronization
                const activitySlider = modal.querySelector('#initialActivitySlider');
                const activityInput = modal.querySelector('#initialActivity');
                const timeSlider = modal.querySelector('#timePeriodSlider');
                const timeInput = modal.querySelector('#timePeriod');
                const speedSlider = modal.querySelector('#animationSpeed');
                const speedDisplay = modal.querySelector('#speedDisplay');

                activitySlider.addEventListener('input', (e) => {
                    activityInput.value = e.target.value;
                });
                activityInput.addEventListener('input', (e) => {
                    activitySlider.value = e.target.value;
                });

                timeSlider.addEventListener('input', (e) => {
                    timeInput.value = e.target.value;
                });
                timeInput.addEventListener('input', (e) => {
                    timeSlider.value = e.target.value;
                });

                speedSlider.addEventListener('input', (e) => {
                    speedDisplay.textContent = e.target.value + 'x';
                });

                // Simulation controls
                modal.querySelector('#runSimBtn').addEventListener('click', () => this.runAdvancedSimulation(modal));
                modal.querySelector('#pauseSimBtn').addEventListener('click', () => this.pauseSimulation());
                modal.querySelector('#resetSimBtn').addEventListener('click', () => this.resetSimulation(modal));
                modal.querySelector('#exportDataBtn').addEventListener('click', () => this.exportSimulationData());

                // Tab switching
                modal.querySelectorAll('.tab-button').forEach(button => {
                    button.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab, modal));
                });

                modal.querySelectorAll('.result-tab').forEach(button => {
                    button.addEventListener('click', (e) => this.switchResultTab(e.target.dataset.result, modal));
                });
            }
        }

            runAdvancedSimulation(modal) {
                const isotope = modal.querySelector('#isotopeSelect').value;
                const initialActivity = parseFloat(modal.querySelector('#initialActivity').value);
                const timePeriod = parseFloat(modal.querySelector('#timePeriod').value);
                const timeUnits = modal.querySelector('#timeUnits').value;
                const animationSpeed = parseFloat(modal.querySelector('#animationSpeed').value);

                this.currentSimulation = {
                    isotope: this.isotopes[isotope],
                    initialActivity: initialActivity,
                    timePeriod: this.convertTimeToHours(timePeriod, timeUnits),
                    animationSpeed: animationSpeed,
                    currentTime: 0,
                    dataPoints: [],
                    isRunning: true
                };

                this.initializeChart(modal);
                this.startRealTimeSimulation(modal);

                // Update button states
                modal.querySelector('#runSimBtn').disabled = true;
                modal.querySelector('#pauseSimBtn').disabled = false;
            }

            convertTimeToHours(time, units) {
                switch(units) {
                    case 'days': return time * 24;
                    case 'years': return time * 24 * 365.25;
                    default: return time; // hours
                }
            }

            initializeChart(modal) {
                const canvas = modal.querySelector('#decayChart');
                const ctx = canvas.getContext('2d');

                this.chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: 'Activity (MBq)',
                            data: [],
                            borderColor: 'rgb(37, 99, 235)',
                            backgroundColor: 'rgba(37, 99, 235, 0.1)',
                            tension: 0.4,
                            fill: true
                        }, {
                            label: 'Nuclei Count (×10²³)',
                            data: [],
                            borderColor: 'rgb(239, 68, 68)',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            tension: 0.4,
                            yAxisID: 'y1'
                        }]
                    },
                    options: {
                        responsive: true,
                        animation: {
                            duration: 0 // Disable animation for real-time updates
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Activity (MBq)'
                                }
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                title: {
                                    display: true,
                                    text: 'Nuclei Count (×10²³)'
                                },
                                grid: {
                                    drawOnChartArea: false,
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false
                            }
                        }
                    }
                });
            }

            startRealTimeSimulation(modal) {
                const updateInterval = 100; // Update every 100ms
                const timeStep = this.currentSimulation.timePeriod / 1000 * this.currentSimulation.animationSpeed;

                this.simulationInterval = setInterval(() => {
                    if (!this.currentSimulation.isRunning) return;

                    this.currentSimulation.currentTime += timeStep;

                    if (this.currentSimulation.currentTime >= this.currentSimulation.timePeriod) {
                        this.stopSimulation();
                        return;
                    }

                    this.updateSimulationData(modal);
                    this.updateRealTimeDisplay(modal);
                    this.updateChart();

                }, updateInterval);
            }

            updateSimulationData(modal) {
                const t = this.currentSimulation.currentTime;
                const lambda = this.currentSimulation.isotope.decayConstant;
                const A0 = this.currentSimulation.initialActivity;

                // Calculate current values
                const currentActivity = A0 * Math.exp(-lambda * t);
                const remainingNuclei = (A0 * 6.022e23 / (lambda * 3600)) * Math.exp(-lambda * t);
                const halfLivesElapsed = t / this.currentSimulation.isotope.halfLife;
                const percentageRemaining = (currentActivity / A0) * 100;

                // Store data point
                this.currentSimulation.dataPoints.push({
                    time: t,
                    activity: currentActivity,
                    nuclei: remainingNuclei,
                    percentage: percentageRemaining
                });

                // Update real-time display
                modal.querySelector('#currentActivity').textContent = currentActivity.toFixed(2) + ' MBq';
                modal.querySelector('#remainingNuclei').textContent = remainingNuclei.toExponential(2);
                modal.querySelector('#halfLivesElapsed').textContent = halfLivesElapsed.toFixed(2);
                modal.querySelector('#percentageRemaining').textContent = percentageRemaining.toFixed(1) + '%';
                modal.querySelector('#timeElapsed').textContent = t.toFixed(1) + ' h';
                modal.querySelector('#decayConstant').textContent = lambda.toFixed(4) + ' h⁻¹';
            }

            updateRealTimeDisplay(modal) {
                // Update data table
                const tableBody = modal.querySelector('#dataTableBody');
                const latestData = this.currentSimulation.dataPoints[this.currentSimulation.dataPoints.length - 1];

                if (this.currentSimulation.dataPoints.length % 10 === 0) { // Add row every 10 data points
                    const row = tableBody.insertRow();
                    row.innerHTML = `
                        <td>${latestData.time.toFixed(1)} h</td>
                        <td>${latestData.activity.toFixed(2)}</td>
                        <td>${latestData.nuclei.toExponential(2)}</td>
                        <td>${latestData.percentage.toFixed(1)}%</td>
                    `;
                }
            }

            updateChart() {
                if (!this.chart) return;

                const data = this.currentSimulation.dataPoints;
                const labels = data.map(d => d.time.toFixed(1));
                const activities = data.map(d => d.activity);
                const nuclei = data.map(d => d.nuclei / 1e23); // Scale for display

                this.chart.data.labels = labels;
                this.chart.data.datasets[0].data = activities;
                this.chart.data.datasets[1].data = nuclei;
                this.chart.update('none'); // No animation for real-time
            }

            pauseSimulation() {
                if (this.currentSimulation) {
                    this.currentSimulation.isRunning = !this.currentSimulation.isRunning;
                }
            }

            stopSimulation() {
                if (this.simulationInterval) {
                    clearInterval(this.simulationInterval);
                    this.simulationInterval = null;
                }
                if (this.currentSimulation) {
                    this.currentSimulation.isRunning = false;
                }
            }

            resetSimulation(modal) {
                this.stopSimulation();
                this.currentSimulation = null;

                // Reset displays
                modal.querySelector('#currentActivity').textContent = '100.0 MBq';
                modal.querySelector('#remainingNuclei').textContent = '6.02×10²³';
                modal.querySelector('#halfLivesElapsed').textContent = '0.0';
                modal.querySelector('#percentageRemaining').textContent = '100.0%';
                modal.querySelector('#timeElapsed').textContent = '0.0 h';

                // Clear table
                modal.querySelector('#dataTableBody').innerHTML = '';

                // Reset chart
                if (this.chart) {
                    this.chart.data.labels = [];
                    this.chart.data.datasets[0].data = [];
                    this.chart.data.datasets[1].data = [];
                    this.chart.update();
                }

                // Reset button states
                modal.querySelector('#runSimBtn').disabled = false;
                modal.querySelector('#pauseSimBtn').disabled = true;
            }

            exportSimulationData() {
                if (!this.currentSimulation || !this.currentSimulation.dataPoints.length) {
                    alert('No simulation data to export');
                    return;
                }

                const data = this.currentSimulation.dataPoints;
                const csv = 'Time (h),Activity (MBq),Nuclei Count,Percentage Remaining\n' +
                    data.map(d => `${d.time.toFixed(3)},${d.activity.toFixed(6)},${d.nuclei.toExponential(6)},${d.percentage.toFixed(3)}`).join('\n');

                const blob = new Blob([csv], { type: 'text/csv' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `decay_simulation_${this.currentSimulation.isotope.name}_${Date.now()}.csv`;
                a.click();
                window.URL.revokeObjectURL(url);
            }

            switchTab(tabName, modal) {
                // Switch tab logic
                modal.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                modal.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

                modal.querySelectorAll('.tab-content').forEach(content => content.style.display = 'none');
                modal.querySelector(`#${tabName}-tab`).style.display = 'block';
            }

            switchResultTab(tabName, modal) {
                modal.querySelectorAll('.result-tab').forEach(btn => btn.classList.remove('active'));
                modal.querySelector(`[data-result="${tabName}"]`).classList.add('active');

                modal.querySelectorAll('.result-panel').forEach(panel => panel.classList.remove('active'));
                modal.querySelector(`#${tabName}`).classList.add('active');
            }
        }

        class DoseCalculator {
            constructor() {
                this.organs = {
                    'liver': { mass: 1800, sValue: 2.31e-4 },
                    'kidneys': { mass: 310, sValue: 1.84e-3 },
                    'lungs': { mass: 1000, sValue: 4.62e-4 },
                    'heart': { mass: 330, sValue: 1.39e-3 },
                    'brain': { mass: 1400, sValue: 3.28e-4 },
                    'thyroid': { mass: 20, sValue: 2.30e-2 }
                };
                this.radiopharmaceuticals = {
                    'Tc-99m-MDP': { halfLife: 6.01, organUptake: { 'liver': 0.05, 'kidneys': 0.15, 'bone': 0.50 } },
                    'I-131-NaI': { halfLife: 192.48, organUptake: { 'thyroid': 0.25, 'liver': 0.05 } },
                    'F-18-FDG': { halfLife: 1.83, organUptake: { 'brain': 0.07, 'heart': 0.03, 'liver': 0.02 } }
                };
            }

            launch() {
                console.log('Launching Advanced Dose Calculator');
                this.createDoseCalculatorInterface();
            }

            createDoseCalculatorInterface() {
                const modal = document.createElement('div');
                modal.className = 'modal tool-modal dose-calculator-modal';
                modal.innerHTML = `
                    <div class="modal-content calculator-content">
                        <span class="close">&times;</span>
                        <h2><i class="fas fa-calculator"></i> MIRD Dose Calculator</h2>

                        <div class="calculator-tabs">
                            <button type="button" class="tab-button active" data-tab="organ">Organ Dose</button>
                            <button type="button" class="tab-button" data-tab="effective">Effective Dose</button>
                            <button type="button" class="tab-button" data-tab="patient">Patient-Specific</button>
                        </div>

                        <div class="tab-content" id="organ-tab">
                            <div class="calculator-inputs">
                                <div class="input-group">
                                    <label>Radiopharmaceutical:</label>
                                    <select id="radiopharmSelect">
                                        <option value="Tc-99m-MDP">Tc-99m-MDP (Bone Scan)</option>
                                        <option value="I-131-NaI">I-131-NaI (Thyroid)</option>
                                        <option value="F-18-FDG">F-18-FDG (PET)</option>
                                    </select>
                                </div>
                                <div class="input-group">
                                    <label>Administered Activity (MBq):</label>
                                    <input type="number" id="adminActivity" value="740" min="1" max="10000">
                                </div>
                                <div class="input-group">
                                    <label>Target Organ:</label>
                                    <select id="targetOrgan">
                                        <option value="liver">Liver</option>
                                        <option value="kidneys">Kidneys</option>
                                        <option value="lungs">Lungs</option>
                                        <option value="heart">Heart</option>
                                        <option value="brain">Brain</option>
                                        <option value="thyroid">Thyroid</option>
                                    </select>
                                </div>
                                <div class="input-group">
                                    <label>Patient Weight (kg):</label>
                                    <input type="number" id="patientWeight" value="70" min="20" max="200">
                                </div>
                                <button type="button" class="btn-primary" onclick="this.calculateOrganDose()">
                                    <i class="fas fa-calculator"></i> Calculate Dose
                                </button>
                            </div>
                        </div>

                        <div class="dose-results">
                            <div class="results-display">
                                <h4>Dosimetry Results</h4>
                                <div class="dose-grid">
                                    <div class="dose-item">
                                        <span class="dose-label">Organ Dose:</span>
                                        <span class="dose-value" id="organDoseResult">- mGy</span>
                                    </div>
                                    <div class="dose-item">
                                        <span class="dose-label">Effective Dose:</span>
                                        <span class="dose-value" id="effectiveDoseResult">- mSv</span>
                                    </div>
                                    <div class="dose-item">
                                        <span class="dose-label">Dose Rate:</span>
                                        <span class="dose-value" id="doseRateResult">- mGy/h</span>
                                    </div>
                                    <div class="dose-item">
                                        <span class="dose-label">Residence Time:</span>
                                        <span class="dose-value" id="residenceTimeResult">- h</span>
                                    </div>
                                </div>
                            </div>
                            <div class="dose-visualization">
                                <canvas id="doseChart" width="400" height="300"></canvas>
                            </div>
                        </div>

                        <div class="mird-equations">
                            <h4>MIRD Methodology</h4>
                            <div class="equation-display">
                                <div class="equation">D = Ã × S</div>
                                <div class="equation-explanation">
                                    Where: D = absorbed dose, Ã = cumulated activity, S = S-value
                                </div>
                                <div class="equation">Ã = A₀ × τ</div>
                                <div class="equation-explanation">
                                    Where: A₀ = initial activity, τ = residence time
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                modal.style.display = 'block';

                this.setupDoseCalculatorEventListeners(modal);

                modal.querySelector('.close').addEventListener('click', () => {
                    modal.style.display = 'none';
                    document.body.removeChild(modal);
                });
            }

            setupDoseCalculatorEventListeners(modal) {
                // Add event listeners for dose calculator
                modal.querySelector('#radiopharmSelect').addEventListener('change', () => this.updateOrganOptions(modal));
                modal.querySelector('#adminActivity').addEventListener('input', () => this.updateRealTimeDose(modal));
                modal.querySelector('#targetOrgan').addEventListener('change', () => this.updateRealTimeDose(modal));
                modal.querySelector('#patientWeight').addEventListener('input', () => this.updateRealTimeDose(modal));
            }

            updateOrganOptions(modal) {
                // Update available organs based on radiopharmaceutical
                const radiopharm = modal.querySelector('#radiopharmSelect').value;
                const organSelect = modal.querySelector('#targetOrgan');

                // Clear current options
                organSelect.innerHTML = '';

                // Add relevant organs
                Object.keys(this.radiopharmaceuticals[radiopharm].organUptake).forEach(organ => {
                    const option = document.createElement('option');
                    option.value = organ;
                    option.textContent = organ.charAt(0).toUpperCase() + organ.slice(1);
                    organSelect.appendChild(option);
                });
            }

            updateRealTimeDose(modal) {
                // Real-time dose calculation as user changes inputs
                setTimeout(() => this.calculateOrganDose(modal), 100);
            }

            calculateOrganDose(modal) {
                const radiopharm = modal.querySelector('#radiopharmSelect').value;
                const activity = parseFloat(modal.querySelector('#adminActivity').value);
                const organ = modal.querySelector('#targetOrgan').value;
                const weight = parseFloat(modal.querySelector('#patientWeight').value);

                // MIRD calculations
                const uptakeFraction = this.radiopharmaceuticals[radiopharm].organUptake[organ] || 0.05;
                const organMass = this.organs[organ]?.mass || 1000; // grams
                const sValue = this.organs[organ]?.sValue || 1e-3; // mGy/MBq·h
                const halfLife = this.radiopharmaceuticals[radiopharm].halfLife; // hours

                // Calculate cumulated activity
                const residenceTime = halfLife / Math.log(2) * uptakeFraction;
                const cumulatedActivity = activity * residenceTime;

                // Calculate absorbed dose
                const organDose = cumulatedActivity * sValue;
                const effectiveDose = organDose * this.getWeightingFactor(organ);
                const doseRate = organDose / residenceTime;

                // Update display
                modal.querySelector('#organDoseResult').textContent = organDose.toFixed(2) + ' mGy';
                modal.querySelector('#effectiveDoseResult').textContent = effectiveDose.toFixed(2) + ' mSv';
                modal.querySelector('#doseRateResult').textContent = doseRate.toFixed(3) + ' mGy/h';
                modal.querySelector('#residenceTimeResult').textContent = residenceTime.toFixed(1) + ' h';

                this.updateDoseVisualization(modal, organDose, effectiveDose);
            }

            getWeightingFactor(organ) {
                const factors = {
                    'liver': 0.04,
                    'kidneys': 0.04,
                    'lungs': 0.12,
                    'heart': 0.01,
                    'brain': 0.01,
                    'thyroid': 0.04
                };
                return factors[organ] || 0.01;
            }

            updateDoseVisualization(modal, organDose, effectiveDose) {
                // Create or update dose visualization chart
                const canvas = modal.querySelector('#doseChart');
                const ctx = canvas.getContext('2d');

                if (this.doseChart) {
                    this.doseChart.destroy();
                }

                this.doseChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['Organ Dose', 'Effective Dose', 'Background (Annual)'],
                        datasets: [{
                            label: 'Dose (mSv)',
                            data: [organDose, effectiveDose, 2.4], // 2.4 mSv annual background
                            backgroundColor: [
                                'rgba(239, 68, 68, 0.8)',
                                'rgba(37, 99, 235, 0.8)',
                                'rgba(34, 197, 94, 0.8)'
                            ],
                            borderColor: [
                                'rgb(239, 68, 68)',
                                'rgb(37, 99, 235)',
                                'rgb(34, 197, 94)'
                            ],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Dose (mSv)'
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return context.parsed.y.toFixed(3) + ' mSv';
                                    }
                                }
                            }
                        }
                    }
                });
            }
        }

        class ImageReconstruction {
            launch() {
                console.log('Launching Image Reconstruction Demo');
                alert('Interactive image reconstruction demonstration would be launched here.');
            }
        }

        class DetectorResponse {
            launch() {
                console.log('Launching Detector Response Analyzer');
                alert('Detector response analysis tool would be launched here.');
            }
        }

        class PhantomStudies {
            launch() {
                console.log('Launching Phantom Studies');
                alert('Virtual phantom studies interface would be launched here.');
            }
        }

        class KineticModeling {
            launch() {
                console.log('Launching Kinetic Modeling Tool');
                alert('Compartmental modeling and kinetic analysis tool would be launched here.');
            }
        }

        // 3D Tool Wrapper Classes
        class AtomicStructure3DTool {
            constructor() {
                this.visualization = null;
            }

            launch() {
                console.log('Launching 3D Atomic Structure Visualization');
                this.create3DModal();
            }

            create3DModal() {
                const modal = document.createElement('div');
                modal.className = 'modal tool-modal atomic-3d-modal';
                modal.innerHTML = `
                    <div class="modal-content visualization-content">
                        <span class="close">&times;</span>
                        <h2><i class="fas fa-atom"></i> 3D Atomic Structure Visualization</h2>

                        <div class="visualization-controls">
                            <div class="control-group">
                                <label>Atom Type:</label>
                                <select id="atomType">
                                    <option value="hydrogen">Hydrogen (H)</option>
                                    <option value="carbon">Carbon (C)</option>
                                    <option value="oxygen">Oxygen (O)</option>
                                    <option value="technetium">Technetium-99m</option>
                                </select>
                            </div>
                            <div class="control-group">
                                <label>Animation Speed:</label>
                                <input type="range" id="animSpeed" min="0.1" max="2" step="0.1" value="1">
                                <span id="speedValue">1.0x</span>
                            </div>
                            <div class="control-group">
                                <button type="button" class="btn-primary" onclick="this.resetView()">
                                    <i class="fas fa-redo"></i> Reset View
                                </button>
                                <button type="button" class="btn-secondary" onclick="this.toggleElectrons()">
                                    <i class="fas fa-eye"></i> Toggle Electrons
                                </button>
                            </div>
                        </div>

                        <div class="visualization-container">
                            <div id="atomic3DContainer" class="threejs-container"></div>
                            <div class="visualization-info">
                                <h4>Atomic Properties</h4>
                                <div class="property-grid">
                                    <div class="property-item">
                                        <span class="property-label">Atomic Number:</span>
                                        <span class="property-value" id="atomicNumber">1</span>
                                    </div>
                                    <div class="property-item">
                                        <span class="property-label">Mass Number:</span>
                                        <span class="property-value" id="massNumber">1</span>
                                    </div>
                                    <div class="property-item">
                                        <span class="property-label">Electrons:</span>
                                        <span class="property-value" id="electronCount">1</span>
                                    </div>
                                    <div class="property-item">
                                        <span class="property-label">Neutrons:</span>
                                        <span class="property-value" id="neutronCount">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="educational-content">
                            <h4>Educational Information</h4>
                            <div id="atomicInfo">
                                <p>Hydrogen is the simplest atom with one proton and one electron. The electron orbits the nucleus in quantum mechanical orbitals.</p>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                modal.style.display = 'block';

                // Initialize 3D visualization
                this.visualization = new AtomicStructure3D();
                this.visualization.init('atomic3DContainer');

                // Setup event listeners
                this.setup3DEventListeners(modal);

                modal.querySelector('.close').addEventListener('click', () => {
                    if (this.visualization) {
                        this.visualization.destroy();
                    }
                    modal.style.display = 'none';
                    document.body.removeChild(modal);
                });
            }

            setup3DEventListeners(modal) {
                modal.querySelector('#atomType').addEventListener('change', (e) => {
                    this.updateAtomType(e.target.value);
                });

                modal.querySelector('#animSpeed').addEventListener('input', (e) => {
                    modal.querySelector('#speedValue').textContent = e.target.value + 'x';
                    // Update animation speed in 3D visualization
                });
            }

            updateAtomType(atomType) {
                const atomData = {
                    hydrogen: { number: 1, mass: 1, electrons: 1, neutrons: 0 },
                    carbon: { number: 6, mass: 12, electrons: 6, neutrons: 6 },
                    oxygen: { number: 8, mass: 16, electrons: 8, neutrons: 8 },
                    technetium: { number: 43, mass: 99, electrons: 43, neutrons: 56 }
                };

                const data = atomData[atomType];
                if (data) {
                    document.getElementById('atomicNumber').textContent = data.number;
                    document.getElementById('massNumber').textContent = data.mass;
                    document.getElementById('electronCount').textContent = data.electrons;
                    document.getElementById('neutronCount').textContent = data.neutrons;
                }
            }
        }

        class GammaCamera3DTool {
            constructor() {
                this.visualization = null;
            }

            launch() {
                console.log('Launching 3D Gamma Camera Visualization');
                this.create3DModal();
            }

            create3DModal() {
                const modal = document.createElement('div');
                modal.className = 'modal tool-modal camera-3d-modal';
                modal.innerHTML = `
                    <div class="modal-content visualization-content">
                        <span class="close">&times;</span>
                        <h2><i class="fas fa-camera"></i> 3D Gamma Camera System</h2>

                        <div class="visualization-controls">
                            <div class="control-group">
                                <label>Collimator Type:</label>
                                <select id="collimatorType">
                                    <option value="parallel">Parallel Hole</option>
                                    <option value="converging">Converging</option>
                                    <option value="diverging">Diverging</option>
                                </select>
                            </div>
                            <div class="control-group">
                                <label>Source Activity:</label>
                                <input type="range" id="sourceActivity" min="1" max="100" value="50">
                                <span id="activityValue">50 MBq</span>
                            </div>
                            <div class="control-group">
                                <button type="button" class="btn-primary" onclick="this.startAcquisition()">
                                    <i class="fas fa-play"></i> Start Acquisition
                                </button>
                                <button type="button" class="btn-secondary" onclick="this.resetCamera()">
                                    <i class="fas fa-redo"></i> Reset Camera
                                </button>
                            </div>
                        </div>

                        <div class="visualization-container">
                            <div id="camera3DContainer" class="threejs-container"></div>
                            <div class="camera-stats">
                                <h4>Detection Statistics</h4>
                                <div class="stats-grid">
                                    <div class="stat-item">
                                        <span class="stat-label">Photons Detected:</span>
                                        <span class="stat-value" id="photonsDetected">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Count Rate:</span>
                                        <span class="stat-value" id="countRate">0 cps</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Detection Efficiency:</span>
                                        <span class="stat-value" id="efficiency">0%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="educational-content">
                            <h4>Gamma Camera Components</h4>
                            <div class="component-info">
                                <div class="component-item">
                                    <h5>Collimator</h5>
                                    <p>Defines the field of view and spatial resolution of the camera.</p>
                                </div>
                                <div class="component-item">
                                    <h5>Scintillation Crystal</h5>
                                    <p>Converts gamma rays to visible light photons.</p>
                                </div>
                                <div class="component-item">
                                    <h5>PMT Array</h5>
                                    <p>Photomultiplier tubes detect and amplify light signals.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                modal.style.display = 'block';

                // Initialize 3D visualization
                this.visualization = new GammaCamera3D();
                this.visualization.init('camera3DContainer');

                // Setup event listeners
                this.setup3DEventListeners(modal);

                modal.querySelector('.close').addEventListener('click', () => {
                    if (this.visualization) {
                        this.visualization.destroy();
                    }
                    modal.style.display = 'none';
                    document.body.removeChild(modal);
                });
            }

            setup3DEventListeners(modal) {
                modal.querySelector('#sourceActivity').addEventListener('input', (e) => {
                    modal.querySelector('#activityValue').textContent = e.target.value + ' MBq';
                });
            }
        }

        // Notification System Class
        class NotificationSystem {
            constructor() {
                this.container = null;
                this.notifications = [];
            }

            init() {
                this.createContainer();
            }

            createContainer() {
                this.container = document.createElement('div');
                this.container.className = 'notification-container';
                document.body.appendChild(this.container);
            }

            show(options) {
                const notification = document.createElement('div');
                notification.className = `notification ${options.type || 'info'}`;

                notification.innerHTML = `
                    <div class="notification-title">${options.title}</div>
                    <div class="notification-message">${options.message}</div>
                    ${options.actions ? `
                        <div class="notification-actions">
                            ${options.actions.map(action =>
                                `<button type="button" class="notification-action ${action.type || 'secondary'}"
                                 onclick="${action.action.toString()}">${action.text}</button>`
                            ).join('')}
                        </div>
                    ` : ''}
                `;

                this.container.appendChild(notification);
                this.notifications.push(notification);

                // Auto-remove after 5 seconds if no actions
                if (!options.actions) {
                    setTimeout(() => this.remove(notification), 5000);
                }

                return notification;
            }

            remove(notification) {
                if (notification && notification.parentNode) {
                    notification.style.animation = 'slideOutRight 0.3s ease';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            this.container.removeChild(notification);
                        }
                        const index = this.notifications.indexOf(notification);
                        if (index > -1) {
                            this.notifications.splice(index, 1);
                        }
                    }, 300);
                }
            }

            hide() {
                this.notifications.forEach(notification => this.remove(notification));
            }
        }

        // Search Engine Class
        class SearchEngine {
            constructor() {
                this.searchData = [];
                this.searchInput = null;
                this.searchResults = null;
            }

            init() {
                this.buildSearchIndex();
                this.createSearchInterface();
            }

            buildSearchIndex() {
                // Build search index from chapter content
                this.searchData = [
                    { title: 'Atomic Structure', content: 'quantum mechanical models nuclear composition binding energy', section: 'chapters', chapter: '1' },
                    { title: 'Radioactive Decay', content: 'decay law half-life activity calculation', section: 'chapters', chapter: '2' },
                    { title: 'Radiation Interaction', content: 'photon interaction compton scattering photoelectric effect', section: 'chapters', chapter: '3' },
                    { title: 'Detection Principles', content: 'detector physics energy resolution efficiency', section: 'chapters', chapter: '4' },
                    { title: 'Gamma Camera', content: 'anger camera collimator spatial resolution', section: 'chapters', chapter: '5' },
                    { title: 'SPECT PET Imaging', content: 'tomographic reconstruction attenuation correction', section: 'chapters', chapter: '6' },
                    { title: 'Radiopharmaceuticals', content: 'tracer kinetics compartmental modeling pharmacokinetics', section: 'chapters', chapter: '7' },
                    { title: 'Image Processing', content: 'digital filtering segmentation quantitative analysis', section: 'chapters', chapter: '8' },
                    { title: 'Radiation Dosimetry', content: 'MIRD methodology organ dose radiation protection', section: 'chapters', chapter: '9' },
                    { title: 'Molecular Imaging', content: 'theranostics molecular probes targeted therapy', section: 'chapters', chapter: '10' },
                    { title: 'Artificial Intelligence', content: 'machine learning deep learning automated diagnosis', section: 'chapters', chapter: '11' },
                    { title: 'Quality Control', content: 'QC protocols regulatory compliance accreditation', section: 'chapters', chapter: '12' }
                ];
            }

            createSearchInterface() {
                const searchContainer = document.createElement('div');
                searchContainer.className = 'search-container';
                searchContainer.innerHTML = `
                    <div class="search-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <input type="text" class="search-input" placeholder="Search chapters, equations, concepts..." id="globalSearch">
                    <div class="search-results" id="searchResults"></div>
                `;

                // Insert search at the top of the navigation
                const navigation = document.querySelector('.navigation');
                if (navigation) {
                    navigation.insertBefore(searchContainer, navigation.firstChild);
                }

                this.searchInput = document.getElementById('globalSearch');
                this.searchResults = document.getElementById('searchResults');

                this.setupSearchEventListeners();
            }

            setupSearchEventListeners() {
                this.searchInput.addEventListener('input', (e) => {
                    const query = e.target.value.trim();
                    if (query.length > 2) {
                        this.performSearch(query);
                    } else {
                        this.hideResults();
                    }
                });

                this.searchInput.addEventListener('focus', () => {
                    if (this.searchInput.value.length > 2) {
                        this.searchResults.style.display = 'block';
                    }
                });

                document.addEventListener('click', (e) => {
                    if (!e.target.closest('.search-container')) {
                        this.hideResults();
                    }
                });
            }

            performSearch(query) {
                const results = this.searchData.filter(item =>
                    item.title.toLowerCase().includes(query.toLowerCase()) ||
                    item.content.toLowerCase().includes(query.toLowerCase())
                );

                this.displayResults(results, query);
            }

            displayResults(results, query) {
                if (results.length === 0) {
                    this.searchResults.innerHTML = '<div class="search-result-item">No results found</div>';
                } else {
                    this.searchResults.innerHTML = results.map(result => `
                        <div class="search-result-item" onclick="NuclearMedicineApp.navigateToResult('${result.section}', '${result.chapter}')">
                            <div class="search-result-title">${this.highlightText(result.title, query)}</div>
                            <div class="search-result-snippet">${this.highlightText(result.content, query)}</div>
                        </div>
                    `).join('');
                }
                this.searchResults.style.display = 'block';
            }

            highlightText(text, query) {
                const regex = new RegExp(`(${query})`, 'gi');
                return text.replace(regex, '<strong>$1</strong>');
            }

            hideResults() {
                this.searchResults.style.display = 'none';
            }

            focus() {
                if (this.searchInput) {
                    this.searchInput.focus();
                }
            }
        }

        // Theme Manager Class
        class ThemeManager {
            constructor() {
                this.currentTheme = 'light';
                this.themes = {
                    light: {
                        name: 'Light',
                        colors: {
                            primary: '#2563eb',
                            secondary: '#7c3aed',
                            accent: '#06b6d4'
                        }
                    },
                    dark: {
                        name: 'Dark',
                        colors: {
                            primary: '#60a5fa',
                            secondary: '#a78bfa',
                            accent: '#34d399'
                        }
                    },
                    blue: {
                        name: 'Ocean Blue',
                        colors: {
                            primary: '#0ea5e9',
                            secondary: '#3b82f6',
                            accent: '#06b6d4'
                        }
                    },
                    green: {
                        name: 'Forest Green',
                        colors: {
                            primary: '#10b981',
                            secondary: '#059669',
                            accent: '#34d399'
                        }
                    }
                };
            }

            init() {
                this.loadSavedTheme();
                this.createThemeSelector();
            }

            loadSavedTheme() {
                const savedTheme = localStorage.getItem('nuclearMedicineTheme');
                if (savedTheme && this.themes[savedTheme]) {
                    this.applyTheme(savedTheme);
                }
            }

            createThemeSelector() {
                const themeSelector = document.createElement('div');
                themeSelector.className = 'theme-selector';
                themeSelector.innerHTML = `
                    <button type="button" class="theme-button" id="themeToggle">
                        <i class="fas fa-palette"></i>
                    </button>
                    <div class="theme-options" id="themeOptions">
                        ${Object.entries(this.themes).map(([key, theme]) => `
                            <div class="theme-option" onclick="NuclearMedicineApp.themeManager.applyTheme('${key}')">
                                <div class="theme-color" style="background: ${theme.colors.primary}"></div>
                                <span>${theme.name}</span>
                            </div>
                        `).join('')}
                    </div>
                `;

                document.body.appendChild(themeSelector);

                document.getElementById('themeToggle').addEventListener('click', () => {
                    const options = document.getElementById('themeOptions');
                    options.style.display = options.style.display === 'block' ? 'none' : 'block';
                });

                document.addEventListener('click', (e) => {
                    if (!e.target.closest('.theme-selector')) {
                        document.getElementById('themeOptions').style.display = 'none';
                    }
                });
            }

            applyTheme(themeName) {
                if (!this.themes[themeName]) return;

                this.currentTheme = themeName;
                document.documentElement.setAttribute('data-theme', themeName);

                const theme = this.themes[themeName];
                const root = document.documentElement;

                Object.entries(theme.colors).forEach(([key, value]) => {
                    root.style.setProperty(`--${key}-color`, value);
                });

                localStorage.setItem('nuclearMedicineTheme', themeName);
                document.getElementById('themeOptions').style.display = 'none';

                // Show notification
                NuclearMedicineApp.notifications.show({
                    type: 'success',
                    title: 'Theme Changed',
                    message: `Applied ${theme.name} theme successfully!`
                });
            }

            toggleTheme() {
                const nextTheme = this.currentTheme === 'light' ? 'dark' : 'light';
                this.applyTheme(nextTheme);
            }
        }

        // Progress Tracker Class
        class ProgressTracker {
            constructor() {
                this.progress = {};
                this.timeSpent = {};
                this.startTime = Date.now();
            }

            init() {
                this.loadProgress();
                this.startTracking();
            }

            loadProgress() {
                const saved = localStorage.getItem('nuclearMedicineProgress');
                if (saved) {
                    this.progress = JSON.parse(saved);
                }
            }

            saveProgress() {
                localStorage.setItem('nuclearMedicineProgress', JSON.stringify(this.progress));
            }

            trackChapterProgress(chapterId, percentage) {
                this.progress[chapterId] = percentage;
                this.saveProgress();
                this.updateProgressDisplay();
            }

            trackTimeSpent(section) {
                const now = Date.now();
                if (!this.timeSpent[section]) {
                    this.timeSpent[section] = 0;
                }
                this.timeSpent[section] += now - this.startTime;
                this.startTime = now;
            }

            startTracking() {
                setInterval(() => {
                    this.trackTimeSpent(NuclearMedicineApp.currentSection);
                }, 60000); // Track every minute
            }

            updateProgressDisplay() {
                const totalProgress = Object.values(this.progress).reduce((sum, val) => sum + val, 0) / Object.keys(this.progress).length || 0;

                const progressFill = document.getElementById('progressFill');
                if (progressFill) {
                    progressFill.style.width = totalProgress + '%';
                    progressFill.classList.add('progress-bar-animated');
                }
            }

            getOverallProgress() {
                const chapters = Object.keys(this.progress);
                if (chapters.length === 0) return 0;

                const total = chapters.reduce((sum, chapter) => sum + this.progress[chapter], 0);
                return total / chapters.length;
            }
        }

        // Enhanced Application Methods
        NuclearMedicineApp.navigateToResult = function(section, chapter) {
            this.showSection(section);
            if (chapter) {
                setTimeout(() => {
                    this.showChapterDetails(chapter);
                }, 300);
            }
            this.searchEngine.hideResults();
        };

        NuclearMedicineApp.startRandomQuiz = function() {
            const quizzes = ['atomic-structure', 'decay-processes', 'detection-principles'];
            const randomQuiz = quizzes[Math.floor(Math.random() * quizzes.length)];
            this.notifications.show({
                type: 'info',
                title: 'Random Quiz',
                message: `Starting quiz on ${randomQuiz.replace('-', ' ')}...`
            });
        };

        NuclearMedicineApp.startRandomSimulation = function() {
            const simulations = ['decay-simulator', 'dose-calculator', 'image-reconstruction'];
            const randomSim = simulations[Math.floor(Math.random() * simulations.length)];
            openTool(randomSim);
        };

        NuclearMedicineApp.showKeyboardShortcuts = function() {
            this.notifications.show({
                type: 'info',
                title: 'Keyboard Shortcuts',
                message: `
                    Ctrl+H: Show this help<br>
                    Ctrl+S: Focus search<br>
                    Ctrl+Q: Random quiz<br>
                    Ctrl+T: Toggle theme<br>
                    Alt****: Navigate sections
                `
            });
        };

        // Global functions for backward compatibility
        function showModal(type) {
            const modal = document.getElementById(type + 'Modal');
            if (modal) modal.style.display = 'block';
        }

        function closeModal(modal) {
            if (modal) modal.style.display = 'none';
        }

        function openTool(toolId) {
            if (NuclearMedicineApp.tools[toolId]) {
                NuclearMedicineApp.tools[toolId].launch();
            } else {
                console.log(`Tool ${toolId} not found`);
            }
        }

        // 3D Visualization Classes using Three.js
        class AtomicStructure3D {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.atoms = [];
                this.electrons = [];
                this.animationId = null;
            }

            init(containerId) {
                this.createScene();
                this.createAtom();
                this.setupLighting();
                this.setupControls();
                this.animate();

                const container = document.getElementById(containerId);
                if (container) {
                    container.appendChild(this.renderer.domElement);
                }
            }

            createScene() {
                // Scene setup
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x0a0a0a);

                // Camera setup
                this.camera = new THREE.PerspectiveCamera(75, 800 / 600, 0.1, 1000);
                this.camera.position.set(0, 0, 10);

                // Renderer setup
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.renderer.setSize(800, 600);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            }

            createAtom() {
                // Create nucleus
                const nucleusGeometry = new THREE.SphereGeometry(0.5, 32, 32);
                const nucleusMaterial = new THREE.MeshPhongMaterial({
                    color: 0xff4444,
                    shininess: 100,
                    transparent: true,
                    opacity: 0.8
                });
                const nucleus = new THREE.Mesh(nucleusGeometry, nucleusMaterial);
                nucleus.castShadow = true;
                this.scene.add(nucleus);

                // Create electron orbitals
                this.createElectronOrbitals();

                // Create electrons
                this.createElectrons();
            }

            createElectronOrbitals() {
                const orbitalRadii = [2, 3.5, 5];

                orbitalRadii.forEach((radius, index) => {
                    const orbitalGeometry = new THREE.RingGeometry(radius - 0.05, radius + 0.05, 64);
                    const orbitalMaterial = new THREE.MeshBasicMaterial({
                        color: 0x4488ff,
                        transparent: true,
                        opacity: 0.3,
                        side: THREE.DoubleSide
                    });

                    const orbital = new THREE.Mesh(orbitalGeometry, orbitalMaterial);
                    orbital.rotation.x = Math.random() * Math.PI;
                    orbital.rotation.y = Math.random() * Math.PI;
                    this.scene.add(orbital);
                });
            }

            createElectrons() {
                const electronGeometry = new THREE.SphereGeometry(0.1, 16, 16);
                const electronMaterial = new THREE.MeshPhongMaterial({
                    color: 0x44ff44,
                    emissive: 0x002200
                });

                const orbitalRadii = [2, 3.5, 5];
                const electronsPerOrbital = [2, 8, 8];

                orbitalRadii.forEach((radius, orbitalIndex) => {
                    for (let i = 0; i < electronsPerOrbital[orbitalIndex]; i++) {
                        const electron = new THREE.Mesh(electronGeometry, electronMaterial);

                        const angle = (i / electronsPerOrbital[orbitalIndex]) * Math.PI * 2;
                        electron.position.set(
                            Math.cos(angle) * radius,
                            Math.sin(angle) * radius,
                            0
                        );

                        electron.userData = {
                            orbital: orbitalIndex,
                            angle: angle,
                            radius: radius,
                            speed: 0.01 + orbitalIndex * 0.005
                        };

                        this.electrons.push(electron);
                        this.scene.add(electron);
                    }
                });
            }

            setupLighting() {
                // Ambient light
                const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
                this.scene.add(ambientLight);

                // Directional light
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(10, 10, 5);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                this.scene.add(directionalLight);

                // Point light for glow effect
                const pointLight = new THREE.PointLight(0x4488ff, 0.5, 100);
                pointLight.position.set(0, 0, 0);
                this.scene.add(pointLight);
            }

            setupControls() {
                // Mouse controls for camera rotation
                let mouseDown = false;
                let mouseX = 0;
                let mouseY = 0;

                this.renderer.domElement.addEventListener('mousedown', (event) => {
                    mouseDown = true;
                    mouseX = event.clientX;
                    mouseY = event.clientY;
                });

                this.renderer.domElement.addEventListener('mouseup', () => {
                    mouseDown = false;
                });

                this.renderer.domElement.addEventListener('mousemove', (event) => {
                    if (!mouseDown) return;

                    const deltaX = event.clientX - mouseX;
                    const deltaY = event.clientY - mouseY;

                    this.camera.position.x = Math.cos(deltaX * 0.01) * 10;
                    this.camera.position.z = Math.sin(deltaX * 0.01) * 10;
                    this.camera.position.y += deltaY * 0.01;

                    this.camera.lookAt(0, 0, 0);

                    mouseX = event.clientX;
                    mouseY = event.clientY;
                });

                // Zoom with mouse wheel
                this.renderer.domElement.addEventListener('wheel', (event) => {
                    const scale = event.deltaY > 0 ? 1.1 : 0.9;
                    this.camera.position.multiplyScalar(scale);
                    this.camera.lookAt(0, 0, 0);
                });
            }

            animate() {
                this.animationId = requestAnimationFrame(() => this.animate());

                // Animate electrons
                this.electrons.forEach(electron => {
                    electron.userData.angle += electron.userData.speed;
                    electron.position.set(
                        Math.cos(electron.userData.angle) * electron.userData.radius,
                        Math.sin(electron.userData.angle) * electron.userData.radius,
                        Math.sin(electron.userData.angle * 2) * 0.5
                    );
                });

                // Rotate the entire atom slowly
                this.scene.rotation.y += 0.005;

                this.renderer.render(this.scene, this.camera);
            }

            destroy() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }
                if (this.renderer) {
                    this.renderer.dispose();
                }
            }
        }

        class GammaCamera3D {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.detector = null;
                this.collimator = null;
                this.photons = [];
                this.animationId = null;
            }

            init(containerId) {
                this.createScene();
                this.createGammaCamera();
                this.createPhotonSource();
                this.setupLighting();
                this.setupControls();
                this.animate();

                const container = document.getElementById(containerId);
                if (container) {
                    container.appendChild(this.renderer.domElement);
                }
            }

            createScene() {
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x1a1a1a);

                this.camera = new THREE.PerspectiveCamera(75, 800 / 600, 0.1, 1000);
                this.camera.position.set(10, 5, 10);
                this.camera.lookAt(0, 0, 0);

                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.renderer.setSize(800, 600);
                this.renderer.shadowMap.enabled = true;
            }

            createGammaCamera() {
                // Create detector crystal
                const detectorGeometry = new THREE.BoxGeometry(8, 6, 0.5);
                const detectorMaterial = new THREE.MeshPhongMaterial({
                    color: 0x88ccff,
                    transparent: true,
                    opacity: 0.7
                });
                this.detector = new THREE.Mesh(detectorGeometry, detectorMaterial);
                this.detector.position.set(0, 0, -3);
                this.scene.add(this.detector);

                // Create collimator
                this.createCollimator();

                // Create PMT array
                this.createPMTArray();
            }

            createCollimator() {
                const collimatorGroup = new THREE.Group();

                // Collimator holes
                for (let x = -3; x <= 3; x += 0.5) {
                    for (let y = -2; y <= 2; y += 0.5) {
                        const holeGeometry = new THREE.CylinderGeometry(0.1, 0.1, 2, 8);
                        const holeMaterial = new THREE.MeshPhongMaterial({
                            color: 0x333333,
                            transparent: true,
                            opacity: 0.8
                        });
                        const hole = new THREE.Mesh(holeGeometry, holeMaterial);
                        hole.position.set(x, y, -1);
                        hole.rotation.x = Math.PI / 2;
                        collimatorGroup.add(hole);
                    }
                }

                this.collimator = collimatorGroup;
                this.scene.add(this.collimator);
            }

            createPMTArray() {
                const pmtGroup = new THREE.Group();

                for (let x = -3; x <= 3; x += 1.5) {
                    for (let y = -2; y <= 2; y += 1.5) {
                        const pmtGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.3, 16);
                        const pmtMaterial = new THREE.MeshPhongMaterial({
                            color: 0xffaa44,
                            metalness: 0.8,
                            roughness: 0.2
                        });
                        const pmt = new THREE.Mesh(pmtGeometry, pmtMaterial);
                        pmt.position.set(x, y, -4);
                        pmtGroup.add(pmt);
                    }
                }

                this.scene.add(pmtGroup);
            }

            createPhotonSource() {
                // Create a radioactive source
                const sourceGeometry = new THREE.SphereGeometry(0.2, 16, 16);
                const sourceMaterial = new THREE.MeshPhongMaterial({
                    color: 0xff4444,
                    emissive: 0x440000
                });
                const source = new THREE.Mesh(sourceGeometry, sourceMaterial);
                source.position.set(0, 0, 5);
                this.scene.add(source);

                // Continuously emit photons
                setInterval(() => {
                    this.emitPhoton();
                }, 100);
            }

            emitPhoton() {
                const photonGeometry = new THREE.SphereGeometry(0.05, 8, 8);
                const photonMaterial = new THREE.MeshBasicMaterial({
                    color: 0x00ff00,
                    emissive: 0x004400
                });
                const photon = new THREE.Mesh(photonGeometry, photonMaterial);

                // Random emission direction (roughly towards detector)
                const angle = (Math.random() - 0.5) * Math.PI / 4;
                photon.position.set(0, 0, 5);
                photon.userData = {
                    velocity: new THREE.Vector3(
                        Math.sin(angle) * 0.1,
                        (Math.random() - 0.5) * 0.1,
                        -0.2
                    )
                };

                this.photons.push(photon);
                this.scene.add(photon);
            }

            setupLighting() {
                const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
                this.scene.add(ambientLight);

                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(10, 10, 10);
                this.scene.add(directionalLight);
            }

            setupControls() {
                // Similar mouse controls as atomic structure
                let mouseDown = false;
                let mouseX = 0;
                let mouseY = 0;

                this.renderer.domElement.addEventListener('mousedown', (event) => {
                    mouseDown = true;
                    mouseX = event.clientX;
                    mouseY = event.clientY;
                });

                this.renderer.domElement.addEventListener('mouseup', () => {
                    mouseDown = false;
                });

                this.renderer.domElement.addEventListener('mousemove', (event) => {
                    if (!mouseDown) return;

                    const deltaX = event.clientX - mouseX;
                    const deltaY = event.clientY - mouseY;

                    const spherical = new THREE.Spherical();
                    spherical.setFromVector3(this.camera.position);
                    spherical.theta -= deltaX * 0.01;
                    spherical.phi += deltaY * 0.01;
                    spherical.phi = Math.max(0.1, Math.min(Math.PI - 0.1, spherical.phi));

                    this.camera.position.setFromSpherical(spherical);
                    this.camera.lookAt(0, 0, 0);

                    mouseX = event.clientX;
                    mouseY = event.clientY;
                });
            }

            animate() {
                this.animationId = requestAnimationFrame(() => this.animate());

                // Animate photons
                this.photons.forEach((photon, index) => {
                    photon.position.add(photon.userData.velocity);

                    // Remove photons that hit the detector or go too far
                    if (photon.position.z < -3 || photon.position.z > 10) {
                        this.scene.remove(photon);
                        this.photons.splice(index, 1);
                    }
                });

                this.renderer.render(this.scene, this.camera);
            }

            destroy() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }
                if (this.renderer) {
                    this.renderer.dispose();
                }
            }
        }

        // Additional CSS animations
        const additionalStyles = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .tool-modal .modal-content {
                max-width: 900px;
                width: 95%;
            }

            .simulator-content {
                padding: 2rem;
            }

            .simulator-controls {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
                margin-bottom: 2rem;
                padding: 1rem;
                background: var(--light-color);
                border-radius: var(--border-radius);
            }

            .control-group {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
            }

            .control-group label {
                font-weight: 600;
                color: var(--text-primary);
            }

            .control-group input, .control-group select {
                padding: 0.5rem;
                border: 1px solid var(--border-color);
                border-radius: 0.25rem;
                font-size: 1rem;
            }

            .simulation-results {
                margin-top: 2rem;
            }

            .results-table {
                margin-top: 1rem;
                padding: 1rem;
                background: var(--light-color);
                border-radius: var(--border-radius);
            }

            .btn-primary, .btn-secondary {
                padding: 0.75rem 1.5rem;
                border: none;
                border-radius: var(--border-radius);
                cursor: pointer;
                font-size: 1rem;
                transition: var(--transition);
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                margin: 0.5rem;
            }

            .btn-primary {
                background: var(--primary-color);
                color: white;
            }

            .btn-primary:hover {
                background: var(--secondary-color);
                transform: translateY(-2px);
            }

            .btn-secondary {
                background: var(--light-color);
                color: var(--text-primary);
                border: 1px solid var(--border-color);
            }

            .btn-secondary:hover {
                background: var(--border-color);
            }

            .chapter-details {
                max-height: 70vh;
                overflow-y: auto;
                padding: 1rem;
            }

            .concept-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1rem;
                margin: 1rem 0;
            }

            .concept-item {
                padding: 1rem;
                background: var(--light-color);
                border-radius: var(--border-radius);
                border-left: 4px solid var(--primary-color);
            }

            .equation-block {
                background: white;
                padding: 1.5rem;
                border-radius: var(--border-radius);
                border: 1px solid var(--border-color);
                margin: 1rem 0;
            }

            .chapter-actions, .presentation-actions {
                display: flex;
                justify-content: center;
                gap: 1rem;
                margin-top: 2rem;
                padding-top: 1rem;
                border-top: 1px solid var(--border-color);
            }

            /* Responsive Design */
            @media (max-width: 768px) {
                .nav-menu {
                    display: none;
                    flex-direction: column;
                    position: absolute;
                    top: 100%;
                    left: 0;
                    right: 0;
                    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
                    border-radius: 0 0 var(--border-radius) var(--border-radius);
                    box-shadow: var(--shadow-heavy);
                }

                .nav-menu.active {
                    display: flex;
                }

                .nav-toggle {
                    display: block;
                }

                .chapter-grid, .presentations-grid, .tools-grid {
                    grid-template-columns: 1fr;
                }

                .overview-grid {
                    grid-template-columns: 1fr;
                }

                .simulator-controls {
                    grid-template-columns: 1fr;
                }
            }
        `;

        // Inject additional styles
        const styleSheet = document.createElement('style');
        styleSheet.textContent = additionalStyles;
        document.head.appendChild(styleSheet);

        // Initialize the application when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            NuclearMedicineApp.init();

            // Add smooth scrolling for better UX
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // Add intersection observer for animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.animation = 'fadeInUp 0.6s ease forwards';
                    }
                });
            }, observerOptions);

            // Observe all cards for animation
            document.querySelectorAll('.chapter-card, .presentation-card, .tool-card, .overview-card').forEach(card => {
                observer.observe(card);
            });

            console.log('Nuclear Medicine Physics Educational Module fully loaded and ready!');
        });
    </script>
</body>
</html>