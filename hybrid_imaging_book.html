<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التصوير الهجين: فيزياء وتطبيقات PET/CT و SPECT/CT</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            text-align: right;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .book-title {
            font-size: 2.5em;
            color: #fff;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .author-info {
            color: #e0e0e0;
            font-size: 1.1em;
            margin-bottom: 10px;
        }

        .contact-info {
            color: #c0c0c0;
            font-size: 0.9em;
            margin-bottom: 20px;
        }

        .description {
            color: #f0f0f0;
            font-size: 1em;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
        }

        .navigation {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .nav-btn.active {
            background: rgba(255, 255, 255, 0.4);
            color: #333;
        }

        .content-area {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            color: #333;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            min-height: 500px;
        }

        .content-section {
            display: none;
            animation: fadeIn 0.5s ease-in-out;
        }

        .content-section.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .chapter-list {
            list-style: none;
            padding: 0;
        }

        .chapter-item {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            margin: 15px 0;
            padding: 20px;
            border-radius: 15px;
            border-right: 5px solid #667eea;
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .chapter-item:hover {
            transform: translateX(-5px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .chapter-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .chapter-subtitle {
            color: #666;
            font-size: 1em;
            margin-bottom: 8px;
        }

        .chapter-content {
            color: #555;
            font-size: 0.95em;
            line-height: 1.5;
        }

        .part-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            padding: 20px;
            border-radius: 15px;
            margin: 30px 0 20px 0;
            text-align: center;
            font-size: 1.4em;
            font-weight: bold;
        }

        .target-audience {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border-right: 5px solid #ff6b6b;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .feature-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .feature-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .feature-content {
            color: #555;
            line-height: 1.6;
        }

        .contact-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
        }

        .contact-section h3 {
            margin-bottom: 15px;
            font-size: 1.4em;
        }

        .contact-item {
            margin: 10px 0;
            font-size: 1.1em;
        }

        .search-box {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 1em;
            margin-bottom: 20px;
            text-align: right;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
        }

        .highlight {
            background-color: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
        }

        @media (max-width: 768px) {
            .book-title {
                font-size: 2em;
            }
            
            .navigation {
                flex-direction: column;
                align-items: center;
            }
            
            .nav-btn {
                width: 200px;
                margin: 5px 0;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="book-title">التصوير الهجين: فيزياء وتطبيقات PET/CT و SPECT/CT</h1>
            <div class="author-info">
                <strong>المؤلف:</strong> د. محمد يعقوب إسماعيل يعقوب
            </div>
            <div class="author-info">
                نائب عميد كلية الهندسة - أستاذ مساعد الهندسة الطبية الحيوية
            </div>
            <div class="author-info">
                جامعة السودان للعلوم والتكنولوجيا
            </div>
            <div class="contact-info">
                © 2025 - البريد الإلكتروني: <EMAIL>
                <br>
                الهاتف: +966538076790
            </div>
            <div class="description">
                مرجع متقدم ومتكامل، مصمم لتقديم فهم عميق للفيزياء التآزرية والتطبيقات السريرية لأنظمة التصوير الهجين. يركز على فيزياء وتقنيات الدمج بين التصوير الوظيفي والتشريحي، موضحًا كيف يعزز هذا الاندماج الدقة التشخيصية.
            </div>
        </div>

        <div class="navigation">
            <button class="nav-btn active" onclick="showSection('overview')">نظرة عامة</button>
            <button class="nav-btn" onclick="showSection('structure')">هيكل الكتاب</button>
            <button class="nav-btn" onclick="showSection('chapters')">الفصول</button>
            <button class="nav-btn" onclick="showSection('requirements')">المتطلبات</button>
            <button class="nav-btn" onclick="showSection('contact')">تواصل معنا</button>
        </div>

        <div class="content-area">
            <div id="overview" class="content-section active">
                <input type="text" class="search-box" placeholder="البحث في المحتوى..." oninput="searchContent(this.value)">
                
                <div class="target-audience">
                    <h3>الجمهور المستهدف:</h3>
                    <p>الفيزيائيون الطبيون، أطباء الأشعة والطب النووي، أطباء الأورام وأمراض القلب والأعصاب، تقنيو الطب النووي والأشعة المقطعية، وطلاب الدراسات العليا والباحثون في مجال التصوير الطبي.</p>
                </div>

                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-title">🔬 فيزياء متقدمة</div>
                        <div class="feature-content">
                            شرح مفصل لفيزياء التصوير الهجين وكيفية دمج التقنيات المختلفة لتحسين الدقة التشخيصية.
                        </div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-title">🏥 تطبيقات سريرية</div>
                        <div class="feature-content">
                            تطبيقات شاملة في علم الأورام، طب القلب، وطب الأعصاب مع دراسات حالة مفصلة.
                        </div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-title">⚙️ تقنيات متطورة</div>
                        <div class="feature-content">
                            تغطية شاملة لأحدث التقنيات مثل Time-of-Flight PET و 4D PET/CT.
                        </div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-title">🔍 ضمان الجودة</div>
                        <div class="feature-content">
                            بروتوكولات مفصلة لضمان الجودة وحل المشاكل التقنية الشائعة.
                        </div>
                    </div>
                </div>
            </div>

            <div id="structure" class="content-section">
                <h2>هيكل الكتاب</h2>
                
                <div class="part-header">الجزء الأول: أسس فيزياء التصوير الهجين</div>
                <p>يغطي المبادئ الأساسية لفيزياء التصوير الوظيفي والتشريحي، والقيود المتأصلة في كل تقنية على حدة.</p>

                <div class="part-header">الجزء الثاني: فيزياء وتقنية أنظمة SPECT/CT</div>
                <p>يركز على تكامل أجهزة SPECT/CT وتقنيات إعادة البناء وتصحيح التوهين.</p>

                <div class="part-header">الجزء الثالث: فيزياء وتقنية أنظمة PET/CT</div>
                <p>يشرح تقنيات PET/CT المتقدمة بما في ذلك Time-of-Flight وحساب قيم SUV.</p>

                <div class="part-header">الجزء الرابع: التطبيقات السريرية المتقدمة</div>
                <p>يستعرض التطبيقات الواقعية في علم الأورام، طب القلب، وطب الأعصاب.</p>

                <div class="part-header">الجزء الخامس: ضمان الجودة والآفاق المستقبلية</div>
                <p>يغطي مراقبة الجودة، العيوب الشائعة، والتطورات المستقبلية مثل PET/MRI.</p>
            </div>

            <div id="chapters" class="content-section">
                <h2>فصول الكتاب</h2>
                <ul class="chapter-list" id="chapterList">
                    <li class="chapter-item" onclick="toggleChapter(this)">
                        <div class="chapter-title">الفصل الأول: مراجعة مركزة لفيزياء التصوير الوظيفي</div>
                        <div class="chapter-subtitle">مبادئ SPECT و PET من إنتاج الفوتون إلى الكشف</div>
                        <div class="chapter-content">
                            يغطي هذا الفصل المبادئ الأساسية لـ SPECT و PET، القيود المتأصلة في التصوير الوظيفي مثل الدقة المكانية المحدودة، وغياب التفاصيل التشريحية.
                        </div>
                    </li>
                    <li class="chapter-item" onclick="toggleChapter(this)">
                        <div class="chapter-title">الفصل الثاني: مراجعة مركزة لفيزياء التصوير المقطعي المحوسب</div>
                        <div class="chapter-subtitle">مبادئ تكوين الصورة في CT ووحدات هاونسفيلد</div>
                        <div class="chapter-content">
                            يشرح مبادئ تكوين الصورة في CT، مفهوم وحدات هاونسفيلد، ودور CT في توفير الخرائط التشريحية عالية الدقة.
                        </div>
                    </li>
                    <li class="chapter-item" onclick="toggleChapter(this)">
                        <div class="chapter-title">الفصل الثالث: تكامل أجهزة SPECT/CT</div>
                        <div class="chapter-subtitle">التصاميم الهندسية وبروتوكولات التصوير</div>
                        <div class="chapter-content">
                            يغطي التصاميم الهندسية للأنظمة المدمجة، مواءمة محاور الدوران، وبروتوكولات الحصول على الصور المتسلسلة.
                        </div>
                    </li>
                    <li class="chapter-item" onclick="toggleChapter(this)">
                        <div class="chapter-title">الفصل الرابع: إعادة البناء وتصحيح التوهين في SPECT/CT</div>
                        <div class="chapter-subtitle">استخدام خرائط CT لتوليد خرائط التوهين</div>
                        <div class="chapter-content">
                            يشرح استخدام خرائط CT لتوليد خرائط التوهين لفوتونات SPECT، وتحديات تحويل وحدات هاونسفيلد إلى معاملات توهين.
                        </div>
                    </li>
                    <li class="chapter-item" onclick="toggleChapter(this)">
                        <div class="chapter-title">الفصل الخامس: تكامل أجهزة PET/CT</div>
                        <div class="chapter-subtitle">فيزياء كواشف PET الحديثة ومفهوم زمن الرحلة</div>
                        <div class="chapter-content">
                            يغطي فيزياء وتصميم كواشف PET الحديثة، مفهوم زمن الرحلة (TOF) وتأثيره على تحسين نسبة الإشارة إلى الضوضاء.
                        </div>
                    </li>
                    <li class="chapter-item" onclick="toggleChapter(this)">
                        <div class="chapter-title">الفصل السادس: إعادة البناء وتصحيح التوهين في PET/CT</div>
                        <div class="chapter-subtitle">التحليل الكمي وحساب قيم SUV</div>
                        <div class="chapter-content">
                            يشرح توليد خرائط التوهين لفوتونات الإفناء، التحليل الكمي لحساب قيم SUV، وتأثير حركة المريض.
                        </div>
                    </li>
                    <li class="chapter-item" onclick="toggleChapter(this)">
                        <div class="chapter-title">الفصل السابع: التطبيقات في علم الأورام</div>
                        <div class="chapter-subtitle">التشخيص وتحديد المراحل وتقييم الاستجابة</div>
                        <div class="chapter-content">
                            يغطي التشخيص الأولي، تحديد المراحل، تقييم الاستجابة للعلاج، مع دراسات حالة لسرطان الرئة والليمفوما.
                        </div>
                    </li>
                    <li class="chapter-item" onclick="toggleChapter(this)">
                        <div class="chapter-title">الفصل الثامن: التطبيقات في طب القلب</div>
                        <div class="chapter-subtitle">تقييم تروية وحيوية عضلة القلب</div>
                        <div class="chapter-content">
                            يشرح تقييم تروية وحيوية عضلة القلب، دمج صور SPECT/PET مع تصوير الأوعية التاجية المقطعي.
                        </div>
                    </li>
                    <li class="chapter-item" onclick="toggleChapter(this)">
                        <div class="chapter-title">الفصل التاسع: التطبيقات في طب الأعصاب</div>
                        <div class="chapter-subtitle">تشخيص الأمراض العصبية التنكسية</div>
                        <div class="chapter-content">
                            يغطي تشخيص الأمراض العصبية التنكسية، تحديد بؤر الصرع، والتمييز بين أنواع الخرف.
                        </div>
                    </li>
                    <li class="chapter-item" onclick="toggleChapter(this)">
                        <div class="chapter-title">الفصل العاشر: ضمان الجودة والعيوب</div>
                        <div class="chapter-subtitle">بروتوكولات الجودة وكتالوج العيوب</div>
                        <div class="chapter-content">
                            يشرح بروتوكولات ضمان الجودة للأنظمة المدمجة وكتالوج مصور للعيوب الفريدة للتصوير الهجين.
                        </div>
                    </li>
                    <li class="chapter-item" onclick="toggleChapter(this)">
                        <div class="chapter-title">الفصل الحادي عشر: قياس الجرعات الإشعاعية</div>
                        <div class="chapter-subtitle">تقدير الجرعة واستراتيجيات التحسين</div>
                        <div class="chapter-content">
                            يغطي تقدير الجرعة الإشعاعية للمريض من مصدرين، واستراتيجيات تحسين البروتوكول لتقليل الجرعة.
                        </div>
                    </li>
                    <li class="chapter-item" onclick="toggleChapter(this)">
                        <div class="chapter-title">الفصل الثاني عشر: الآفاق المستقبلية</div>
                        <div class="chapter-subtitle">PET/MRI والذكاء الاصطناعي</div>
                        <div class="chapter-content">
                            يشرح فيزياء وتحديات أنظمة PET/MRI، المزايا الفريدة، ودور الذكاء الاصطناعي في تطوير التصوير الهجين.
                        </div>
                    </li>
                </ul>
            </div>

            <div id="requirements" class="content-section">
                <h2>متطلبات الكتاب</h2>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-title">📊 الرسومات والأشكال</div>
                        <div class="feature-content">
                            <ul>
                                <li>مخططات مقارنة توضح مسار الفوتون في الأنسجة</li>
                                <li>صور ثلاثية الأبعاد لاندماج الصور الوظيفية والتشريحية</li>
                                <li>رسومات تخطيطية لعملية تحويل وحدات هاونسفيلد</li>
                                <li>دراسات حالة مصورة مقارنة</li>
                                <li>كتالوج مصور للعيوب مع شرح الأسباب</li>
                            </ul>
                        </div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-title">📋 الجداول</div>
                        <div class="feature-content">
                            <ul>
                                <li>جداول مقارنة مفصلة بين PET/CT و SPECT/CT</li>
                                <li>جداول مقارنة بين PET/CT و PET/MRI</li>
                                <li>جداول بروتوكولات التصوير للحالات المختلفة</li>
                                <li>جداول الجرعات الإشعاعية والتوصيات</li>
                            </ul>
                        </div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-title">🔢 المعادلات</div>
                        <div class="feature-content">
                            <ul>
                                <li>معادلة تحويل وحدات هاونسفيلد (HU) إلى معامل التوهين الخطي (μ)</li>
                                <li>معادلة حساب قيمة الامتصاص المعيارية (SUV)</li>
                                <li>معادلات أساسية في فيزياء TOF-PET</li>
                                <li>معادلات تصحيح التوهين والتشتت</li>
                            </ul>
                        </div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-title">📚 المراجع</div>
                        <div class="feature-content">
                            <ul>
                                <li>أحدث البيانات من الجمعيات الدولية</li>
                                <li>توصيات SNMMI و EANM</li>
                                <li>معايير AAPM</li>
                                <li>نقاط سريرية في نهاية كل فصل</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div id="contact" class="content-section">
                <div class="contact-section">
                    <h3>تواصل مع المؤلف</h3>
                    <div class="contact-item">
                        <strong>Dr. Mohammed Yagoub Esmail</strong>
                    </div>
                    <div class="contact-item">
                        SUST - BME
                    </div>
                    <div class="contact-item">
                        📧 البريد الإلكتروني: <EMAIL>
                    </div>
                    <div class="contact-item">
                        📱 الهاتف: +249912867327
                    </div>
                    <div class="contact-item">
                        📱 الهاتف: +966538076790
                    </div>
                </div>
                
                <div class="feature-card" style="margin-top: 20px;">
                    <div class="feature-title">حقوق النشر</div>
                    <div class="feature-content">
                        جميع الحقوق محفوظة © 2025 للدكتور محمد يعقوب إسماعيل. هذا الكتاب محمي بموجب قوانين حقوق الطبع والنشر الدولية.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId) {
            // Hide all sections
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });
            
            // Remove active class from all buttons
            const buttons = document.querySelectorAll('.nav-btn');
            buttons.forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionId).classList.add('active');
            
            // Add active class to clicked button
            event.target.classList.add('active');
        }

        function toggleChapter(element) {
            const content = element.querySelector('.chapter-content');
            const isVisible = content.style.display === 'block';
            
            // Hide all chapter contents
            const allContents = document.querySelectorAll('.chapter-content');
            allContents.forEach(content => {
                content.style.display = 'none';
            });
            
            // Toggle current chapter
            if (!isVisible) {
                content.style.display = 'block';
                element.style.backgroundColor = '#e8f4f8';
            } else {
                element.style.backgroundColor = '';
            }
        }

        function searchContent(query) {
            const chapterList = document.getElementById('chapterList');
            const chapters = chapterList.querySelectorAll('.chapter-item');
            
            if (query.trim() === '') {
                chapters.forEach(chapter => {
                    chapter.style.display = 'block';
                    removeHighlight(chapter);
                });
                return;
            }
            
            chapters.forEach(chapter => {
                const text = chapter.textContent.toLowerCase();
                const searchQuery = query.toLowerCase();
                
                if (text.includes(searchQuery)) {
                    chapter.style.display = 'block';
                    highlightText(chapter, query);
                } else {
                    chapter.style.display = 'none';
                    removeHighlight(chapter);
                }
            });
        }

        function highlightText(element, query) {
            const textNodes = getTextNodes(element);
            const regex = new RegExp(`(${query})`, 'gi');
            
            textNodes.forEach(node => {
                if (node.textContent.toLowerCase().includes(query.toLowerCase())) {
                    const parent = node.parentNode;
                    const html = node.textContent.