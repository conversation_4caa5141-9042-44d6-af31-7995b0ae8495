<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Biomedical Engineering in Nuclear Medicine | Dr. <PERSON></title>
    <meta name="description" content="Integration of biomedical engineering principles with nuclear medicine applications, instrumentation design, and system optimization">
    <meta name="keywords" content="Biomedical Engineering, Nuclear Medicine, Medical Instrumentation, System Design, SUST-BME">
    <meta name="author" content="Dr. <PERSON>, SUST-BME">
    
    <!-- Fonts and Icons -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <!-- Chart.js for interactive visualizations -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- MathJax for equations -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #7c3aed;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --light-color: #f8fafc;
            --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-heavy: 0 20px 25px rgba(0, 0, 0, 0.1);
            --border-radius: 0.75rem;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --font-primary: 'Inter', sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-primary);
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        /* Header */
        .page-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 4rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
        }

        .breadcrumb {
            margin-bottom: 2rem;
            text-align: center;
        }

        .breadcrumb a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .page-title {
            font-size: clamp(2.5rem, 6vw, 4rem);
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .page-subtitle {
            font-size: 1.5rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }

        /* Main Content */
        .main-content {
            padding: 4rem 0;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 3rem;
        }

        .main-article {
            background: white;
            border-radius: var(--border-radius);
            padding: 3rem;
            box-shadow: var(--shadow-medium);
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .sidebar-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow-medium);
            border-left: 4px solid var(--accent-color);
        }

        .sidebar-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Article Content */
        .article-header {
            margin-bottom: 3rem;
            text-align: center;
            padding-bottom: 2rem;
            border-bottom: 2px solid var(--border-color);
        }

        .article-title {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .article-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section {
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 1.75rem;
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            padding-left: 1rem;
            border-left: 4px solid var(--accent-color);
        }

        .section-content {
            color: var(--text-secondary);
            line-height: 1.8;
            margin-bottom: 2rem;
        }

        /* Interactive Elements */
        .interactive-element {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            padding: 2rem;
            border-radius: var(--border-radius);
            margin: 2rem 0;
            border-left: 4px solid var(--accent-color);
        }

        .element-title {
            font-size: 1.25rem;
            color: var(--accent-color);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .element-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            align-items: center;
        }

        .controls-panel {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .control-group label {
            font-weight: 600;
            color: var(--text-primary);
        }

        .control-group input, .control-group select {
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 0.25rem;
        }

        .visualization-panel {
            background: white;
            padding: 1rem;
            border-radius: 0.5rem;
            min-height: 250px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--border-color);
        }

        /* Tables and Figures */
        .figure-container {
            background: var(--light-color);
            padding: 2rem;
            border-radius: var(--border-radius);
            margin: 2rem 0;
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .figure-title {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .figure-caption {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-top: 1rem;
            font-style: italic;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 2rem 0;
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-medium);
        }

        .data-table th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }

        .data-table td {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table tr:hover {
            background: var(--light-color);
        }

        /* Equations */
        .equation-block {
            background: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            margin: 2rem 0;
            border-left: 4px solid var(--success-color);
            text-align: center;
        }

        .equation-title {
            font-weight: 600;
            color: var(--success-color);
            margin-bottom: 1rem;
        }

        .equation {
            font-family: 'Times New Roman', serif;
            font-size: 1.2rem;
            margin: 1rem 0;
        }

        .equation-description {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-top: 1rem;
        }

        /* Citations */
        .citation {
            background: var(--light-color);
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
            border-left: 3px solid var(--warning-color);
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 1rem;
            margin: 2rem 0;
            justify-content: center;
        }

        .action-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: white;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-secondary:hover {
            background: var(--primary-color);
            color: white;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .content-grid {
                grid-template-columns: 1fr;
            }

            .sidebar {
                order: -1;
            }
        }

        @media (max-width: 768px) {
            .page-header {
                padding: 2rem 0;
            }

            .main-article {
                padding: 2rem;
            }

            .element-content {
                grid-template-columns: 1fr;
            }

            .article-meta {
                flex-direction: column;
                gap: 0.5rem;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <!-- Page Header -->
    <header class="page-header">
        <div class="container">
            <nav class="breadcrumb">
                <a href="../index_new.html">Home</a> / 
                <a href="../nuclear_medicine_book.html">Nuclear Medicine</a> / 
                <span>Biomedical Engineering</span>
            </nav>
            
            <h1 class="page-title">Biomedical Engineering in Nuclear Medicine</h1>
            <p class="page-subtitle">Integration of Engineering Principles with Medical Applications</p>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="content-grid">
                <!-- Main Article -->
                <article class="main-article">
                    <header class="article-header">
                        <h1 class="article-title">Engineering Principles in Nuclear Medicine Systems</h1>
                        <div class="article-meta">
                            <div class="meta-item">
                                <i class="fas fa-user"></i>
                                <span>Dr. Mohammed Yagoub Esmail</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-calendar"></i>
                                <span>January 2025</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-clock"></i>
                                <span>45 min read</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-tag"></i>
                                <span>Advanced Level</span>
                            </div>
                        </div>
                    </header>

                    <!-- Introduction Section -->
                    <section class="section">
                        <h2 class="section-title">Introduction to Biomedical Engineering in Nuclear Medicine</h2>
                        <div class="section-content">
                            <p>
                                Biomedical engineering plays a crucial role in the development and optimization of nuclear medicine 
                                systems. This interdisciplinary field combines engineering principles with biological and medical 
                                sciences to create innovative solutions for healthcare challenges. In nuclear medicine, biomedical 
                                engineers focus on instrumentation design, signal processing, image reconstruction, and system 
                                optimization to improve diagnostic accuracy and patient care.
                            </p>
                            <p>
                                The integration of engineering principles with nuclear medicine has led to significant advances in 
                                detector technology, imaging systems, and computational methods. Modern nuclear medicine systems 
                                incorporate sophisticated electronics, advanced materials, and complex algorithms that require 
                                deep understanding of both engineering and medical physics principles (Cherry et al., 2012).
                            </p>
                        </div>

                        <div class="citation">
                            <strong>Key Reference:</strong> Cherry, S.R., Sorenson, J.A., & Phelps, M.E. (2012). 
                            Physics in Nuclear Medicine (4th ed.). Elsevier Saunders. Chapter 23: "Instrumentation Design and Optimization"
                        </div>
                    </section>

                    <!-- System Design Section -->
                    <section class="section">
                        <h2 class="section-title">Nuclear Medicine System Design</h2>
                        <div class="section-content">
                            <p>
                                The design of nuclear medicine systems requires careful consideration of multiple engineering 
                                factors including detector efficiency, spatial resolution, temporal resolution, and system 
                                sensitivity. Biomedical engineers must balance these competing requirements while considering 
                                cost, reliability, and ease of use.
                            </p>
                        </div>

                        <div class="figure-container">
                            <div class="figure-title">Figure 1: Nuclear Medicine System Architecture</div>
                            <canvas id="systemArchitecture" width="600" height="300"></canvas>
                            <div class="figure-caption">
                                Schematic representation of a modern nuclear medicine imaging system showing the integration 
                                of detector arrays, signal processing electronics, and computational systems.
                            </div>
                        </div>

                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>System Component</th>
                                    <th>Engineering Considerations</th>
                                    <th>Performance Metrics</th>
                                    <th>Optimization Strategies</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Detector Array</td>
                                    <td>Material selection, geometry, coupling</td>
                                    <td>Detection efficiency, energy resolution</td>
                                    <td>Crystal optimization, light collection</td>
                                </tr>
                                <tr>
                                    <td>Electronics</td>
                                    <td>Noise reduction, signal amplification</td>
                                    <td>Signal-to-noise ratio, linearity</td>
                                    <td>Low-noise design, digital processing</td>
                                </tr>
                                <tr>
                                    <td>Data Acquisition</td>
                                    <td>Sampling rate, data throughput</td>
                                    <td>Count rate capability, dead time</td>
                                    <td>Parallel processing, buffering</td>
                                </tr>
                                <tr>
                                    <td>Image Reconstruction</td>
                                    <td>Algorithm efficiency, computational load</td>
                                    <td>Image quality, reconstruction time</td>
                                    <td>GPU acceleration, iterative methods</td>
                                </tr>
                            </tbody>
                        </table>
                    </section>

                    <!-- Signal Processing Section -->
                    <section class="section">
                        <h2 class="section-title">Signal Processing and Electronics</h2>
                        <div class="section-content">
                            <p>
                                Signal processing in nuclear medicine systems involves the conversion of radiation interactions 
                                into digital signals suitable for image formation. This process includes pulse shaping, 
                                amplification, analog-to-digital conversion, and digital signal processing algorithms.
                            </p>
                        </div>

                        <div class="equation-block">
                            <div class="equation-title">Signal Processing Transfer Function</div>
                            <div class="equation">H(ω) = G(ω) × F(ω) × A(ω)</div>
                            <div class="equation-description">
                                Where H(ω) is the overall system transfer function, G(ω) is the detector response, 
                                F(ω) is the filter response, and A(ω) is the amplifier response.
                            </div>
                        </div>

                        <div class="interactive-element">
                            <h3 class="element-title">
                                <i class="fas fa-wave-square"></i>
                                Interactive Signal Processing Demonstration
                            </h3>
                            <div class="element-content">
                                <div class="controls-panel">
                                    <div class="control-group">
                                        <label>Signal Amplitude:</label>
                                        <input type="range" id="amplitudeSlider" min="0.1" max="2" step="0.1" value="1">
                                        <span id="amplitudeValue">1.0</span>
                                    </div>
                                    <div class="control-group">
                                        <label>Noise Level:</label>
                                        <input type="range" id="noiseSlider" min="0" max="0.5" step="0.05" value="0.1">
                                        <span id="noiseValue">0.1</span>
                                    </div>
                                    <div class="control-group">
                                        <label>Filter Type:</label>
                                        <select id="filterSelect">
                                            <option value="none">No Filter</option>
                                            <option value="lowpass">Low-pass</option>
                                            <option value="bandpass">Band-pass</option>
                                            <option value="matched">Matched Filter</option>
                                        </select>
                                    </div>
                                    <div class="control-group">
                                        <button type="button" id="updateSignal" class="action-btn btn-primary">
                                            <i class="fas fa-refresh"></i> Update Signal
                                        </button>
                                    </div>
                                </div>
                                <div class="visualization-panel">
                                    <canvas id="signalChart" width="350" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Quality Control Section -->
                    <section class="section">
                        <h2 class="section-title">Quality Control and Performance Optimization</h2>
                        <div class="section-content">
                            <p>
                                Biomedical engineers are responsible for developing and implementing quality control procedures 
                                that ensure optimal system performance throughout the operational lifetime of nuclear medicine 
                                equipment. This includes routine calibration, performance monitoring, and preventive maintenance 
                                protocols (NEMA Standards, 2018).
                            </p>
                        </div>

                        <div class="citation">
                            <strong>Standards Reference:</strong> NEMA NU 1-2018. Performance Measurements of Gamma Cameras. 
                            National Electrical Manufacturers Association, Rosslyn, VA.
                        </div>
                    </section>

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <a href="../nuclear_medicine_book.html" class="action-btn btn-primary">
                            <i class="fas fa-book"></i>
                            Return to Main Module
                        </a>
                        <a href="#" class="action-btn btn-secondary">
                            <i class="fas fa-download"></i>
                            Download Chapter PDF
                        </a>
                    </div>
                </article>

                <!-- Sidebar -->
                <aside class="sidebar">
                    <!-- Table of Contents -->
                    <div class="sidebar-card">
                        <h3 class="sidebar-title">
                            <i class="fas fa-list"></i>
                            Table of Contents
                        </h3>
                        <ul style="list-style: none; padding: 0;">
                            <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);">
                                <a href="#introduction" style="color: var(--primary-color); text-decoration: none;">Introduction</a>
                            </li>
                            <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);">
                                <a href="#system-design" style="color: var(--primary-color); text-decoration: none;">System Design</a>
                            </li>
                            <li style="padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);">
                                <a href="#signal-processing" style="color: var(--primary-color); text-decoration: none;">Signal Processing</a>
                            </li>
                            <li style="padding: 0.5rem 0;">
                                <a href="#quality-control" style="color: var(--primary-color); text-decoration: none;">Quality Control</a>
                            </li>
                        </ul>
                    </div>

                    <!-- Key Figures -->
                    <div class="sidebar-card">
                        <h3 class="sidebar-title">
                            <i class="fas fa-chart-bar"></i>
                            Key Figures & Tables
                        </h3>
                        <ul style="list-style: none; padding: 0; font-size: 0.9rem;">
                            <li style="padding: 0.25rem 0;">Figure 1: System Architecture</li>
                            <li style="padding: 0.25rem 0;">Table 1: Component Analysis</li>
                            <li style="padding: 0.25rem 0;">Figure 2: Signal Processing Flow</li>
                            <li style="padding: 0.25rem 0;">Interactive Demo: Signal Analysis</li>
                        </ul>
                    </div>

                    <!-- Related Resources -->
                    <div class="sidebar-card">
                        <h3 class="sidebar-title">
                            <i class="fas fa-link"></i>
                            Related Resources
                        </h3>
                        <ul style="list-style: none; padding: 0; font-size: 0.9rem;">
                            <li style="padding: 0.25rem 0;">
                                <a href="../nuclear_medicine_book.html" style="color: var(--primary-color); text-decoration: none;">Nuclear Medicine Physics</a>
                            </li>
                            <li style="padding: 0.25rem 0;">
                                <a href="../nuclear_imaging_physics_advanced.html" style="color: var(--primary-color); text-decoration: none;">Nuclear Imaging Physics</a>
                            </li>
                            <li style="padding: 0.25rem 0;">
                                <a href="#" style="color: var(--primary-color); text-decoration: none;">Interactive Simulations</a>
                            </li>
                        </ul>
                    </div>
                </aside>
            </div>
        </div>
    </main>

    <script>
        // Interactive signal processing demonstration
        document.addEventListener('DOMContentLoaded', function() {
            const amplitudeSlider = document.getElementById('amplitudeSlider');
            const amplitudeValue = document.getElementById('amplitudeValue');
            const noiseSlider = document.getElementById('noiseSlider');
            const noiseValue = document.getElementById('noiseValue');
            const updateButton = document.getElementById('updateSignal');

            // Update display values
            amplitudeSlider.addEventListener('input', function() {
                amplitudeValue.textContent = this.value;
            });

            noiseSlider.addEventListener('input', function() {
                noiseValue.textContent = this.value;
            });

            // Update signal visualization
            updateButton.addEventListener('click', function() {
                // Signal processing simulation would go here
                alert('Signal processing demonstration would update the visualization with the selected parameters.');
            });

            // System architecture diagram
            const canvas = document.getElementById('systemArchitecture');
            if (canvas) {
                const ctx = canvas.getContext('2d');
                
                // Simple system diagram
                ctx.fillStyle = '#2563eb';
                ctx.fillRect(50, 100, 100, 60);
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText('Detector', 70, 135);
                
                ctx.fillStyle = '#7c3aed';
                ctx.fillRect(200, 100, 100, 60);
                ctx.fillStyle = 'white';
                ctx.fillText('Electronics', 215, 135);
                
                ctx.fillStyle = '#06b6d4';
                ctx.fillRect(350, 100, 100, 60);
                ctx.fillStyle = 'white';
                ctx.fillText('Processing', 370, 135);
                
                // Arrows
                ctx.strokeStyle = '#1f2937';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(150, 130);
                ctx.lineTo(200, 130);
                ctx.stroke();
                
                ctx.beginPath();
                ctx.moveTo(300, 130);
                ctx.lineTo(350, 130);
                ctx.stroke();
            }
        });
    </script>
</body>
</html>
