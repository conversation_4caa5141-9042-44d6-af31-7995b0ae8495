<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المنصة الأكاديمية - د. محمد يعقوب إسماعيل يعقوب</title>
    <link rel="stylesheet" href="css/main.css">
    <meta name="description" content="المنصة الأكاديمية للدكتور محمد يعقوب إسماعيل يعقوب - نائب عميد كلية الهندسة، أستاذ مساعد الهندسة الطبية الحيوية، جامعة السودان للعلوم والتكنولوجيا">
</head>
<body class="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-cairo">
    <div class="max-w-4xl w-full mx-auto px-6">
        <div class="text-center bg-white bg-opacity-10 backdrop-blur-lg rounded-3xl p-12 border border-white border-opacity-20 shadow-2xl">
            <!-- Header -->
            <header class="mb-12">
                <h1 class="text-4xl md:text-5xl font-bold mb-4">
                    مرحباً بكم في المنصة الأكاديمية
                </h1>
                <h2 class="text-2xl md:text-3xl font-semibold text-yellow-300 mb-4">
                    د. محمد يعقوب إسماعيل يعقوب
                </h2>
                <p class="text-lg opacity-90 mb-2">
                    نائب عميد كلية الهندسة - أستاذ مساعد الهندسة الطبية الحيوية
                </p>
                <p class="text-lg opacity-90">
                    جامعة السودان للعلوم والتكنولوجيا
                </p>
            </header>

            <!-- Options Grid -->
            <main class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Academic Library Option -->
                <a href="pages/homepage_nuclear_medicine_academic_hub.html" 
                   class="group bg-white bg-opacity-15 rounded-2xl p-8 border border-white border-opacity-20 transition-all duration-300 hover:bg-opacity-25 hover:transform hover:-translate-y-2 hover:shadow-xl block">
                    <div class="text-6xl mb-6">📚</div>
                    <h3 class="text-xl font-bold mb-4 group-hover:text-yellow-300 transition-colors">
                        المكتبة الأكاديمية
                    </h3>
                    <p class="text-base opacity-90 leading-relaxed">
                        استعراض الكتب المنشورة والمراجع العلمية في الطب النووي والفيزياء الطبية
                    </p>
                </a>

                <!-- Authoring Project Option -->
                <a href="book_authoring_hub.html" 
                   class="group bg-white bg-opacity-15 rounded-2xl p-8 border border-white border-opacity-20 transition-all duration-300 hover:bg-opacity-25 hover:transform hover:-translate-y-2 hover:shadow-xl block">
                    <div class="text-6xl mb-6">✍️</div>
                    <h3 class="text-xl font-bold mb-4 group-hover:text-yellow-300 transition-colors">
                        مشروع التأليف
                    </h3>
                    <p class="text-base opacity-90 leading-relaxed">
                        منصة تأليف الكتب العلمية مع نظام إدارة المحتوى والمستودع الرقمي
                    </p>
                </a>
            </main>

            <!-- Footer -->
            <footer class="mt-12 pt-8 border-t border-white border-opacity-20">
                <p class="text-sm opacity-75">
                    © 2025 د. محمد يعقوب إسماعيل يعقوب - جميع الحقوق محفوظة
                </p>
            </footer>
        </div>
    </div>

    <!-- Loading Animation (Optional) -->
    <div id="loading" class="fixed inset-0 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center z-50 transition-opacity duration-500">
        <div class="text-center">
            <div class="animate-spin rounded-full h-16 w-16 border-4 border-white border-opacity-30 border-t-white mx-auto mb-4"></div>
            <h2 class="text-2xl font-bold text-white">المنصة الأكاديمية</h2>
            <p class="text-white opacity-90">جاري تحميل المحتوى...</p>
        </div>
    </div>

    <script>
        // Simple loading animation
        window.addEventListener('load', function() {
            setTimeout(function() {
                const loading = document.getElementById('loading');
                if (loading) {
                    loading.style.opacity = '0';
                    setTimeout(function() {
                        loading.style.display = 'none';
                    }, 500);
                }
            }, 1000);
        });

        // Add click effects
        document.querySelectorAll('a[href]').forEach(function(link) {
            link.addEventListener('click', function(e) {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            const links = document.querySelectorAll('a[href]');
            const currentFocus = document.activeElement;
            const currentIndex = Array.from(links).indexOf(currentFocus);

            if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                e.preventDefault();
                const nextIndex = (currentIndex + 1) % links.length;
                links[nextIndex].focus();
            } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                e.preventDefault();
                const prevIndex = currentIndex === 0 ? links.length - 1 : currentIndex - 1;
                links[prevIndex].focus();
            }
        });
    </script>
</body>
</html>
