<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>المنصة الأكاديمية - د. محمد يعقوب إسماعيل يعقوب</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet" />
    <meta name="description" content="المنصة الأكاديمية للدكتور محمد يعقوب إسماعيل يعقوب - نائب عميد كلية الهندسة، أستاذ مساعد الهندسة الطبية الحيوية، جامعة السودان للعلوم والتكنولوجيا" />
    <meta name="keywords" content="الطب النووي, الفيزياء الطبية, الهندسة الطبية الحيوية, التعليم الطبي, الكتب العلمية" />
    <meta name="author" content="د. محمد يعقوب إسماعيل يعقوب" />
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            overflow-x: hidden;
            line-height: 1.6;
        }

        /* Utility Classes */
        .hidden {
            display: none !important;
        }

        /* Loading Styles */
        .loading-container {
            text-align: center;
            animation: fadeIn 0.5s ease-in;
        }

        .loading-content h1 {
            font-size: clamp(2rem, 5vw, 2.5rem);
            margin-bottom: 10px;
            font-weight: bold;
        }

        .loading-content p {
            margin-bottom: 30px;
            opacity: 0.9;
            font-size: clamp(1rem, 3vw, 1.1rem);
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        /* Optimized Animations */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes fadeIn {
            0% {
                opacity: 0;
                transform: translate3d(0, 20px, 0);
            }
            100% {
                opacity: 1;
                transform: translate3d(0, 0, 0);
            }
        }

        @keyframes slideUp {
            0% {
                opacity: 0;
                transform: translate3d(0, 30px, 0);
            }
            100% {
                opacity: 1;
                transform: translate3d(0, 0, 0);
            }
        }

        /* Selection Container */
        .selection-container {
            width: 100%;
            max-width: 900px;
            padding: 20px;
            animation: slideUp 0.6s ease-out;
        }

        .selection-content {
            text-align: center;
            background: rgba(255,255,255,0.1);
            -webkit-backdrop-filter: blur(15px);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 50px;
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        /* Welcome Header */
        .welcome-header h1 {
            font-size: clamp(2.2rem, 6vw, 2.8rem);
            margin-bottom: 15px;
            font-weight: bold;
        }

        .welcome-header h2 {
            font-size: clamp(1.6rem, 4vw, 2rem);
            margin-bottom: 15px;
            color: #ffd700;
            font-weight: 600;
        }

        .welcome-header p {
            margin-bottom: 10px;
            opacity: 0.9;
            font-size: clamp(1rem, 2.5vw, 1.1rem);
        }

        /* Options Grid */
        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 50px;
        }

        .option-card {
            background: rgba(255,255,255,0.15);
            border-radius: 20px;
            padding: 40px;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
            display: block;
        }

        .option-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .option-card:hover::before {
            opacity: 1;
        }

        .option-card:hover {
            transform: translateY(-8px) scale(1.02);
            background: rgba(255,255,255,0.25);
            box-shadow: 0 15px 40px rgba(0,0,0,0.4);
        }

        .option-card:focus {
            outline: 2px solid #ffd700;
            outline-offset: 2px;
        }

        .option-icon {
            margin-bottom: 25px;
            position: relative;
            z-index: 1;
            display: flex;
            justify-content: center;
        }

        .option-card h3 {
            font-size: clamp(1.4rem, 3vw, 1.6rem);
            margin-bottom: 20px;
            font-weight: bold;
            position: relative;
            z-index: 1;
        }

        .option-card p {
            font-size: clamp(0.9rem, 2vw, 1rem);
            line-height: 1.6;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .options-grid {
                grid-template-columns: 1fr;
                gap: 25px;
                margin-top: 30px;
            }

            .selection-content {
                padding: 30px 20px;
            }

            .option-card {
                padding: 25px;
            }
        }

        @media (max-width: 480px) {
            .selection-content {
                padding: 20px 15px;
                border-radius: 15px;
            }

            .option-card {
                padding: 20px;
                border-radius: 15px;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }

            .loading-spinner {
                animation: none;
            }
        }
    </style>
</head>
<body>
    <div class="loading-container" id="loadingContainer">
        <div class="loading-content">
            <div class="loading-spinner" aria-label="جاري التحميل"></div>
            <h1>المنصة الأكاديمية</h1>
            <p>جاري تحميل المحتوى...</p>
        </div>
    </div>

    <div class="selection-container hidden" id="selectionContainer">
        <div class="selection-content">
            <header class="welcome-header">
                <h1>مرحباً بكم في المنصة الأكاديمية</h1>
                <h2>د. محمد يعقوب إسماعيل يعقوب</h2>
                <p>نائب عميد كلية الهندسة - أستاذ مساعد الهندسة الطبية الحيوية</p>
                <p>جامعة السودان للعلوم والتكنولوجيا</p>
            </header>

            <main class="options-grid">
                <a href="pages/homepage_nuclear_medicine_academic_hub.html" class="option-card" role="button" aria-label="الانتقال إلى المكتبة الأكاديمية">
                    <div class="option-icon" aria-hidden="true">
                        <svg width="48" height="48" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                        </svg>
                    </div>
                    <h3>المكتبة الأكاديمية</h3>
                    <p>استعراض الكتب المنشورة والمراجع العلمية في الطب النووي والفيزياء الطبية</p>
                </a>

                <a href="book_authoring_hub.html" class="option-card" role="button" aria-label="الانتقال إلى مشروع التأليف">
                    <div class="option-icon" aria-hidden="true">
                        <svg width="48" height="48" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                            <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 2V6a1 1 0 112 0v1a1 1 0 11-2 0zm3 0V6a1 1 0 112 0v1a1 1 0 11-2 0z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <h3>مشروع التأليف</h3>
                    <p>منصة تأليف الكتب العلمية مع نظام إدارة المحتوى والمستودع الرقمي</p>
                </a>
            </main>
        </div>
    </div>

    <!-- Fallback for users without JavaScript -->
    <noscript>
        <style>
            .loading-container { display: none !important; }
            .selection-container { display: block !important; }
            .hidden { display: block !important; }
        </style>
        <div style="position: fixed; top: 10px; left: 10px; right: 10px; background: #ff6b6b; color: white; padding: 10px; border-radius: 5px; text-align: center; z-index: 9999;">
            تحتاج إلى تفعيل JavaScript لتجربة أفضل
        </div>
    </noscript>

    <!-- JavaScript -->
    <script>
        // Enhanced page loading with error handling
        document.addEventListener('DOMContentLoaded', function() {
            const loadingContainer = document.getElementById('loadingContainer');
            const selectionContainer = document.getElementById('selectionContainer');

            // Ensure elements exist
            if (!loadingContainer || !selectionContainer) {
                console.error('Required elements not found');
                return;
            }

            // Show selection after loading animation
            setTimeout(function() {
                try {
                    loadingContainer.style.opacity = '0';
                    loadingContainer.style.transform = 'translateY(-20px)';

                    setTimeout(function() {
                        loadingContainer.classList.add('hidden');
                        selectionContainer.classList.remove('hidden');
                        selectionContainer.style.opacity = '0';
                        selectionContainer.style.transform = 'translateY(20px)';

                        // Animate in selection container
                        requestAnimationFrame(function() {
                            selectionContainer.style.transition = 'all 0.6s ease-out';
                            selectionContainer.style.opacity = '1';
                            selectionContainer.style.transform = 'translateY(0)';
                        });
                    }, 300);
                } catch (error) {
                    console.error('Animation error:', error);
                    // Fallback: show selection immediately
                    loadingContainer.classList.add('hidden');
                    selectionContainer.classList.remove('hidden');
                }
            }, 1500);
        });

        // Enhanced keyboard navigation
        document.addEventListener('keydown', function(e) {
            const optionCards = document.querySelectorAll('.option-card');
            const currentFocus = document.activeElement;
            const currentIndex = Array.from(optionCards).indexOf(currentFocus);

            switch(e.key) {
                case 'ArrowRight':
                case 'ArrowDown':
                    e.preventDefault();
                    const nextIndex = (currentIndex + 1) % optionCards.length;
                    optionCards[nextIndex].focus();
                    break;
                case 'ArrowLeft':
                case 'ArrowUp':
                    e.preventDefault();
                    const prevIndex = currentIndex === 0 ? optionCards.length - 1 : currentIndex - 1;
                    optionCards[prevIndex].focus();
                    break;
                case 'Enter':
                case ' ':
                    if (currentFocus && currentFocus.classList.contains('option-card')) {
                        e.preventDefault();
                        currentFocus.click();
                    }
                    break;
            }
        });

        // Add click analytics (optional)
        document.querySelectorAll('.option-card').forEach(function(card) {
            card.addEventListener('click', function(e) {
                const cardTitle = this.querySelector('h3').textContent;
                console.log('Navigation to:', cardTitle);

                // Optional: Add loading state
                this.style.opacity = '0.7';
                this.style.transform = 'scale(0.98)';
            });
        });

        // Preload critical resources
        function preloadResources() {
            const links = [
                'pages/homepage_nuclear_medicine_academic_hub.html',
                'book_authoring_hub.html'
            ];

            links.forEach(function(href) {
                const link = document.createElement('link');
                link.rel = 'prefetch';
                link.href = href;
                document.head.appendChild(link);
            });
        }

        // Initialize preloading after page load
        window.addEventListener('load', preloadResources);

        // Handle reduced motion preference
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            document.documentElement.style.setProperty('--animation-duration', '0.1s');
        }

        // Error handling for missing resources
        window.addEventListener('error', function(e) {
            console.error('Resource loading error:', e.filename, e.message);
        });

        // Service Worker registration (if available)
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                // Uncomment if you have a service worker
                // navigator.serviceWorker.register('/sw.js');
            });
        }
    </script>
</body>
</html>