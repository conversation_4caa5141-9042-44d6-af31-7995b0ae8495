<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nuclear Imaging Physics - Advanced Techniques | Dr. <PERSON></title>
    <meta name="description" content="Comprehensive guide to nuclear imaging physics, reconstruction algorithms, and advanced imaging techniques with interactive demonstrations">
    <meta name="keywords" content="Nuclear Imaging, Medical Physics, SPECT, PET, Image Reconstruction, Biomedical Engineering">
    <meta name="author" content="Dr. <PERSON>, SUST-BME">
    
    <!-- Fonts and Icons -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <!-- Chart.js for interactive visualizations -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- MathJax for equations -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #7c3aed;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --light-color: #f8fafc;
            --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-heavy: 0 20px 25px rgba(0, 0, 0, 0.1);
            --border-radius: 0.75rem;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --font-primary: 'Inter', sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-primary);
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        /* Header */
        .book-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 4rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .book-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
        }

        .breadcrumb {
            margin-bottom: 2rem;
            text-align: center;
        }

        .breadcrumb a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .book-title {
            font-size: clamp(2.5rem, 6vw, 4rem);
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .book-subtitle {
            font-size: 1.5rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }

        .author-info {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            margin: 2rem auto;
            max-width: 600px;
        }

        .author-name {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .author-title {
            opacity: 0.9;
            margin-bottom: 1rem;
        }

        .copyright-info {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* Navigation */
        .book-nav {
            background: white;
            padding: 2rem 0;
            box-shadow: var(--shadow-medium);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .nav-item {
            background: linear-gradient(135deg, var(--light-color), #e2e8f0);
            padding: 1rem;
            border-radius: var(--border-radius);
            text-align: center;
            cursor: pointer;
            transition: var(--transition);
            border-left: 4px solid var(--primary-color);
        }

        .nav-item:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-medium);
            background: linear-gradient(135deg, white, var(--light-color));
        }

        .nav-item.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }

        .nav-icon {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .nav-text {
            font-weight: 600;
            font-size: 0.9rem;
        }

        /* Main Content */
        .main-content {
            padding: 4rem 0;
        }

        .content-section {
            display: none;
            background: white;
            border-radius: var(--border-radius);
            padding: 3rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-medium);
        }

        .content-section.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .section-title {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 2rem;
            text-align: center;
        }

        .section-description {
            font-size: 1.125rem;
            color: var(--text-secondary);
            text-align: center;
            max-width: 800px;
            margin: 0 auto 3rem;
        }

        /* Chapter Grid */
        .chapters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
        }

        .chapter-card {
            background: linear-gradient(135deg, var(--light-color), #e2e8f0);
            border-radius: var(--border-radius);
            padding: 2rem;
            transition: var(--transition);
            border-left: 4px solid var(--accent-color);
            position: relative;
            overflow: hidden;
        }

        .chapter-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-heavy);
        }

        .chapter-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
            border-radius: 50%;
            transform: translate(50%, -50%);
            opacity: 0.1;
        }

        .chapter-number {
            font-size: 3rem;
            font-weight: 800;
            color: var(--accent-color);
            opacity: 0.3;
            position: absolute;
            top: 1rem;
            right: 1rem;
        }

        .chapter-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
        }

        .chapter-content {
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .chapter-features {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .feature-tag {
            background: var(--accent-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .chapter-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .stats-group {
            display: flex;
            gap: 1rem;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .chapter-action {
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .chapter-action:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }

        /* Interactive Elements */
        .interactive-demo {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            padding: 2rem;
            border-radius: var(--border-radius);
            margin: 2rem 0;
            border-left: 4px solid var(--accent-color);
        }

        .demo-title {
            font-size: 1.25rem;
            color: var(--accent-color);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .demo-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            align-items: center;
        }

        .demo-controls {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .control-group label {
            font-weight: 600;
            color: var(--text-primary);
        }

        .control-group input, .control-group select {
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 0.25rem;
        }

        .demo-visualization {
            background: white;
            padding: 1rem;
            border-radius: 0.5rem;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--border-color);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .book-header {
                padding: 2rem 0;
            }

            .nav-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .chapters-grid {
                grid-template-columns: 1fr;
            }

            .demo-content {
                grid-template-columns: 1fr;
            }

            .chapter-stats {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <!-- Book Header -->
    <header class="book-header">
        <div class="container">
            <nav class="breadcrumb">
                <a href="index_new.html">Home</a> / 
                <a href="nuclear_medicine_book.html">Nuclear Medicine</a> / 
                <span>Nuclear Imaging Physics</span>
            </nav>
            
            <h1 class="book-title">Nuclear Imaging Physics</h1>
            <p class="book-subtitle">Advanced Techniques and Reconstruction Algorithms</p>
            
            <div class="author-info">
                <div class="author-name">Dr. Mohammed Yagoub Esmail</div>
                <div class="author-title">
                    Associate Professor of Biomedical Engineering<br>
                    Vice Dean, College of Engineering<br>
                    Sudan University of Science and Technology (SUST)
                </div>
                <div class="copyright-info">
                    © 2025 Dr. Mohammed Yagoub Esmail | All Rights Reserved<br>
                    Email: <EMAIL><br>
                    Phone: +249912867327, +966538076790
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="book-nav">
        <div class="container">
            <div class="nav-grid">
                <div class="nav-item active" data-section="overview">
                    <i class="fas fa-home nav-icon"></i>
                    <span class="nav-text">Overview</span>
                </div>
                <div class="nav-item" data-section="spect">
                    <i class="fas fa-camera nav-icon"></i>
                    <span class="nav-text">SPECT Imaging</span>
                </div>
                <div class="nav-item" data-section="pet">
                    <i class="fas fa-atom nav-icon"></i>
                    <span class="nav-text">PET Imaging</span>
                </div>
                <div class="nav-item" data-section="reconstruction">
                    <i class="fas fa-cogs nav-icon"></i>
                    <span class="nav-text">Reconstruction</span>
                </div>
                <div class="nav-item" data-section="quality">
                    <i class="fas fa-check-circle nav-icon"></i>
                    <span class="nav-text">Quality Control</span>
                </div>
                <div class="nav-item" data-section="clinical">
                    <i class="fas fa-user-md nav-icon"></i>
                    <span class="nav-text">Clinical Applications</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Overview Section -->
            <section id="overview" class="content-section active">
                <h2 class="section-title">Nuclear Imaging Physics Overview</h2>
                <p class="section-description">
                    Comprehensive exploration of nuclear imaging physics principles, from basic detector physics 
                    to advanced reconstruction algorithms and clinical applications in modern nuclear medicine.
                </p>

                <div class="chapters-grid">
                    <div class="chapter-card">
                        <div class="chapter-number">01</div>
                        <h3 class="chapter-title">Fundamentals of Nuclear Imaging</h3>
                        <p class="chapter-content">
                            Introduction to nuclear imaging principles, detector physics, and basic instrumentation. 
                            Covers scintillation detectors, photomultiplier tubes, and signal processing fundamentals 
                            with detailed analysis of energy resolution and detection efficiency (Knoll, 2010).
                        </p>
                        <div class="chapter-features">
                            <span class="feature-tag">Detector Physics</span>
                            <span class="feature-tag">Signal Processing</span>
                            <span class="feature-tag">Instrumentation</span>
                            <span class="feature-tag">Energy Resolution</span>
                        </div>
                        <div class="chapter-stats">
                            <div class="stats-group">
                                <div class="stat-item">
                                    <i class="fas fa-image"></i>
                                    <span>25 Figures</span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-table"></i>
                                    <span>12 Tables</span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-quote-right"></i>
                                    <span>45 Citations</span>
                                </div>
                            </div>
                            <a href="pages/nuclear_imaging_chapter1.html" class="chapter-action">Read Chapter</a>
                        </div>
                    </div>

                    <div class="chapter-card">
                        <div class="chapter-number">02</div>
                        <h3 class="chapter-title">Gamma Camera Systems</h3>
                        <p class="chapter-content">
                            Detailed analysis of gamma camera design, collimation systems, and performance characteristics. 
                            Includes spatial resolution, sensitivity, and uniformity considerations based on Anger's original 
                            design principles (Anger, 1958) and modern digital implementations.
                        </p>
                        <div class="chapter-features">
                            <span class="feature-tag">Anger Camera</span>
                            <span class="feature-tag">Collimators</span>
                            <span class="feature-tag">Performance</span>
                            <span class="feature-tag">Digital Systems</span>
                        </div>
                        <div class="chapter-stats">
                            <div class="stats-group">
                                <div class="stat-item">
                                    <i class="fas fa-image"></i>
                                    <span>32 Figures</span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-table"></i>
                                    <span>18 Tables</span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-quote-right"></i>
                                    <span>62 Citations</span>
                                </div>
                            </div>
                            <a href="pages/nuclear_imaging_chapter2.html" class="chapter-action">Read Chapter</a>
                        </div>
                    </div>
                </div>

                <!-- Interactive Demo -->
                <div class="interactive-demo">
                    <h3 class="demo-title">
                        <i class="fas fa-play-circle"></i>
                        Interactive Gamma Camera Simulation
                    </h3>
                    <div class="demo-content">
                        <div class="demo-controls">
                            <div class="control-group">
                                <label>Source Activity (MBq):</label>
                                <input type="range" id="activitySlider" min="1" max="1000" value="100">
                                <span id="activityValue">100 MBq</span>
                            </div>
                            <div class="control-group">
                                <label>Collimator Type:</label>
                                <select id="collimatorSelect">
                                    <option value="parallel">Parallel Hole</option>
                                    <option value="converging">Converging</option>
                                    <option value="diverging">Diverging</option>
                                </select>
                            </div>
                            <div class="control-group">
                                <button type="button" id="startSimulation" class="chapter-action">
                                    <i class="fas fa-play"></i> Start Simulation
                                </button>
                            </div>
                        </div>
                        <div class="demo-visualization">
                            <canvas id="simulationCanvas" width="300" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <script>
        // Navigation functionality
        document.addEventListener('DOMContentLoaded', function() {
            const navItems = document.querySelectorAll('.nav-item');
            const contentSections = document.querySelectorAll('.content-section');

            navItems.forEach(item => {
                item.addEventListener('click', function() {
                    const targetSection = this.dataset.section;
                    
                    // Update navigation
                    navItems.forEach(nav => nav.classList.remove('active'));
                    this.classList.add('active');
                    
                    // Update content
                    contentSections.forEach(section => section.classList.remove('active'));
                    const targetElement = document.getElementById(targetSection);
                    if (targetElement) {
                        targetElement.classList.add('active');
                    }
                });
            });

            // Interactive demo
            const activitySlider = document.getElementById('activitySlider');
            const activityValue = document.getElementById('activityValue');
            const startSimulation = document.getElementById('startSimulation');

            if (activitySlider && activityValue) {
                activitySlider.addEventListener('input', function() {
                    activityValue.textContent = this.value + ' MBq';
                });
            }

            if (startSimulation) {
                startSimulation.addEventListener('click', function() {
                    // Simulation logic would go here
                    alert('Gamma camera simulation would start here with the selected parameters.');
                });
            }
        });
    </script>
</body>
</html>
