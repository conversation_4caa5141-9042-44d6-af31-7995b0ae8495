module.exports = {
  content: ["./pages/*.{html,js}", "./index.html", "./js/*.js"],
  theme: {
    extend: {
      colors: {
        // Primary Colors - Deep medical authority
        primary: {
          DEFAULT: "#004466", // Deep medical authority, trustworthy foundation
          50: "#f0f8ff", // Very light blue tint
          100: "#e0f2ff", // Light blue tint
          200: "#b3d9ff", // Medium light blue
          300: "#80c4ff", // Medium blue
          400: "#4da6ff", // Medium dark blue
          500: "#1a88ff", // Standard blue
          600: "#0066cc", // Dark blue
          700: "#004466", // Primary - deep medical authority
          800: "#003355", // Darker blue
          900: "#002244", // Darkest blue
        },
        
        // Secondary Colors - Innovation accessibility
        secondary: {
          DEFAULT: "#0077cc", // Innovation accessibility, approachable expertise
          50: "#f0f8ff", // Very light secondary
          100: "#e0f2ff", // Light secondary
          200: "#b3d9ff", // Medium light secondary
          300: "#80c4ff", // Medium secondary
          400: "#4da6ff", // Medium dark secondary
          500: "#0077cc", // Secondary - innovation accessibility
          600: "#0066b3", // Dark secondary
          700: "#005599", // Darker secondary
          800: "#004480", // Very dark secondary
          900: "#003366", // Darkest secondary
        },
        
        // Accent Colors - Attention highlights
        accent: {
          DEFAULT: "#ff6b35", // Attention highlights, breakthrough moments
          50: "#fff5f2", // Very light accent
          100: "#ffebe5", // Light accent
          200: "#ffd6cc", // Medium light accent
          300: "#ffb399", // Medium accent
          400: "#ff9066", // Medium dark accent
          500: "#ff6b35", // Accent - attention highlights
          600: "#e55a2b", // Dark accent
          700: "#cc4a21", // Darker accent
          800: "#b33a17", // Very dark accent
          900: "#992a0d", // Darkest accent
        },
        
        // Background and Surface
        background: "#f8f9fb", // Clean reading canvas, reduces eye strain
        surface: "#ffffff", // Content elevation, clear separation
        
        // Text Colors
        text: {
          primary: "#1a1a1a", // Extended reading comfort, maximum legibility
          secondary: "#666666", // Clear hierarchy, supporting information
        },
        
        // Status Colors
        success: {
          DEFAULT: "#22c55e", // Learning progress, positive reinforcement - green-500
          50: "#f0fdf4", // Very light success - green-50
          100: "#dcfce7", // Light success - green-100
          200: "#bbf7d0", // Medium light success - green-200
          300: "#86efac", // Medium success - green-300
          400: "#4ade80", // Medium dark success - green-400
          500: "#22c55e", // Success - green-500
          600: "#16a34a", // Dark success - green-600
          700: "#15803d", // Darker success - green-700
          800: "#166534", // Very dark success - green-800
          900: "#14532d", // Darkest success - green-900
        },
        
        warning: {
          DEFAULT: "#f59e0b", // Important medical notes, careful attention - amber-500
          50: "#fffbeb", // Very light warning - amber-50
          100: "#fef3c7", // Light warning - amber-100
          200: "#fde68a", // Medium light warning - amber-200
          300: "#fcd34d", // Medium warning - amber-300
          400: "#fbbf24", // Medium dark warning - amber-400
          500: "#f59e0b", // Warning - amber-500
          600: "#d97706", // Dark warning - amber-600
          700: "#b45309", // Darker warning - amber-700
          800: "#92400e", // Very dark warning - amber-800
          900: "#78350f", // Darkest warning - amber-900
        },
        
        error: {
          DEFAULT: "#ef4444", // Critical corrections, helpful guidance - red-500
          50: "#fef2f2", // Very light error - red-50
          100: "#fee2e2", // Light error - red-100
          200: "#fecaca", // Medium light error - red-200
          300: "#fca5a5", // Medium error - red-300
          400: "#f87171", // Medium dark error - red-400
          500: "#ef4444", // Error - red-500
          600: "#dc2626", // Dark error - red-600
          700: "#b91c1c", // Darker error - red-700
          800: "#991b1b", // Very dark error - red-800
          900: "#7f1d1d", // Darkest error - red-900
        },
        
        // Border Colors
        border: {
          DEFAULT: "#e5e7eb", // Clean separation - gray-200
          light: "#f3f4f6", // Subtle separation - gray-100
          dark: "#d1d5db", // Stronger separation - gray-300
        },
      },
      
      fontFamily: {
        cairo: ['Cairo', 'sans-serif'], // Headlines and CTAs
        inter: ['Inter', 'sans-serif'], // Body text
        mono: ['JetBrains Mono', 'monospace'], // Technical content
        sans: ['Inter', 'sans-serif'], // Default sans-serif
      },
      
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1' }],
        '6xl': ['3.75rem', { lineHeight: '1' }],
      },
      
      boxShadow: {
        'soft': '0 4px 12px rgba(0, 68, 102, 0.1)', // Subtle depth for content cards
        'medium': '0 8px 24px rgba(0, 68, 102, 0.15)', // Medium depth for modals
        'strong': '0 16px 48px rgba(0, 68, 102, 0.2)', // Strong depth for overlays
      },
      
      borderRadius: {
        'none': '0',
        'sm': '0.125rem',
        DEFAULT: '0.25rem',
        'md': '0.375rem',
        'lg': '0.5rem',
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
        'full': '9999px',
      },
      
      transitionDuration: {
        '200': '200ms', // Quick transitions for micro-interactions
        '300': '300ms', // Smooth transitions for hover states
        '500': '500ms',
        '700': '700ms',
        '1000': '1000ms',
      },
      
      transitionTimingFunction: {
        'ease-out': 'cubic-bezier(0, 0, 0.2, 1)',
        'ease-in': 'cubic-bezier(0.4, 0, 1, 1)',
        'ease-in-out': 'cubic-bezier(0.4, 0, 0.2, 1)',
      },
      
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      
      animation: {
        'fade-in': 'fadeIn 0.3s ease-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'pulse-soft': 'pulseSoft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        pulseSoft: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.8' },
        },
      },
    },
  },
  plugins: [],
}